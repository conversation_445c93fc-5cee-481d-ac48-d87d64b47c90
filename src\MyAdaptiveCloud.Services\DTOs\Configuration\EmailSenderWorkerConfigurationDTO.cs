﻿namespace MyAdaptiveCloud.Services.DTOs.Configuration
{
    public class EmailSenderWorkerConfigurationDTO : WorkerConfigurationBase
    {
        public const string Category = "SMTP";

        public string Server { get; set; }

        public string Email { get; set; }

        public string Password { get; set; }

        public int Port { get; set; }

        public string UserName { get; set; }

        public bool SendApproval { get; set; }

        public string SenderEmail { get; set; }

        public List<string> RegistrationEmail { get; set; }
    }
}