namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class CifsService: OntapObject
{
    public Svm Svm { get; set; }
    public CifsAdDomain AdDomain { get; set; }
    public CifsNetbios Netbios { get; set; }

    public CifsService () {}

    public CifsService(Guid svm, string name, string fqdn, string user, string password)
    {
        Name = name;
        Svm = new Svm(svm);
        AdDomain = new CifsAdDomain(fqdn, user, password);
        Netbios = new CifsNetbios(true, new List<string> { name });        
    }

    public CifsService(string user, string password)
    {
        AdDomain = new CifsAdDomain(user, password);
    }
}

public class CifsAdDomain
{
    public string Fqdn { get; set; }
    public string User { get; set; }
    public string Password { get; set; }
    
    public CifsAdDomain() {}

    public CifsAdDomain(string user, string password)
    {
        User = user;
        Password = password;
    }

    public CifsAdDomain(string fqdn, string user, string password)
    {
        Fqdn = fqdn;
        User = user;
        Password = password;
    }
}

public class CifsNetbios {
    public List<string> Aliases { get; set; } = new List<string>();
    public bool Enabled { get; set; } = true;

    public CifsNetbios() { }

    public CifsNetbios(bool enabled, List<string> aliases) {
        Enabled = enabled; 
        Aliases = aliases;
    }
}

public class CifsServiceUpdate
{
    public string Name { get; set; }

    public bool Enabled { get; set; } = false;

    public CifsAdDomain AdDomain { get; set; }
    public CifsNetbios Netbios { get; set; }

    public CifsServiceUpdate() { }

    public CifsServiceUpdate(string name, string fqdn, string user, string password)
    {
        Name = name;
        AdDomain = new CifsAdDomain(fqdn, user, password);
        Netbios = new CifsNetbios(true, new List<string> { name });
    }   
}