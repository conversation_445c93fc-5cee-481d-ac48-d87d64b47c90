﻿using MyAdaptiveCloud.Services.Apis.Acronis.Enums;
using MyAdaptiveCloud.Services.Apis.Acronis.Model;

namespace MyAdaptiveCloud.Services.Apis.Acronis
{
    public interface IAcronisApi
    {
        public delegate Task InvalidCredentials(IDataProtectionCredential credentials);

        InvalidCredentials InvalidCredentialsDelegate { set; }
        Task<AcronisClient> GetClient(IDataProtectionCredential credentials);

        Task<AcronisTenant> GetTenant(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisTenant> CreateTenant(string organizationName, bool isPartner, string parentTenantId, string companyShortName, IDataProtectionCredential credentials);

        Task DeleteTenant(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisResultBatch<AcronisApplication>> GetApplications(IDataProtectionCredential credentials);

        Task EnableApplication(string applicationId, string tenantId, IDataProtectionCredential credentials);

        Task EnableOfferingItems<T>(string applicationId, string tenantId, List<T> items, List<Guid> infraId, IDataProtectionCredential credentials) where T : Enum;

        Task<AcronisResultBatch<AcronisOfferingItem>> GetOfferingItems(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisUser> CreateUser(string tenantId, string email, string firstname, string lastname, List<string> types, IDataProtectionCredential credentials);

        Task<AcronisResultBatch<AcronisAccessPolicy>> GetUserAccessPolicies(string acronisUserId, IDataProtectionCredential credentials);

        Task<AcronisResultBatch<AcronisAccessPolicy>> UpdateUserAccessPolicies(string acronisUserId, List<Dictionary<string, object>> policies,
            IDataProtectionCredential credentials);

        Task<AcronisResultBatch<AcronisTenant>> GetTenantChildren(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisResultBatch<string>> GetTenantUsers(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisUser> GetUser(string userId, IDataProtectionCredential credentials);

        Task<AcronisTenantLocations> GetTenantAvailableLocations(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisLocation> GetLocation(string locationId, IDataProtectionCredential credentials);

        Task<AcronisInfrastructure> GetInfrastructureInfo(string infraId, IDataProtectionCredential credentials);

        Task<bool> IsValidLogin(string login, IDataProtectionCredential credentials);

        Task UpdatePricingMode(string tenantId, AcronisPricingMode pricingMode, long version, IDataProtectionCredential credentials);

        Task<AcronisTenantPricing> GetTenantPricing(string tenantId, IDataProtectionCredential credentials);

        Task<AcronisLocationInfrastructures> GetLocationInfras(string locationId, IDataProtectionCredential credentials);

        Task<AcronisResultBatch<AcronisInfrastructure>> GetAvailableInfrastructures(string uuids, IDataProtectionCredential credentials);
        Task DeleteUser(string userId, IDataProtectionCredential credentials);

        Task<AcronisUser> UpdateUser(string userId, bool enabled, string email, string firstname, string lastname, int version, IDataProtectionCredential credentials);

        Task<AcronisClient> CreateClient(string tenantId, string organizationName, IDataProtectionCredential credentials);

        //Task SetCredentialsOrganizationId(int? organizationId);

        Task<AcronisTenant> UpdateTenant(string tenantId, string organizationName, string parentTenantId, string companyShortName, bool? enabled, int version,
            bool? ancestralAccess, IDataProtectionCredential credentials);

        Task SendActivationEmail(string acronisUserId, IDataProtectionCredential credentials);
    }
}