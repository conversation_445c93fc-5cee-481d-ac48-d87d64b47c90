﻿namespace MyAdaptiveCloud.Services.DTOs.AdaptiveCloud
{
    public class UsageSummaryAccountModel
    {

        public Guid AccountId { get; set; }

        public string AccountName { get; set; }

        public double AccountTotal { get; set; }

        public UsageSummaryAccountDetailModel VCpu { get; set; }

        public UsageSummaryAccountDetailModel Ram { get; set; }

        public UsageSummaryAccountDetailModel IpAddress { get; set; }

        public UsageSummaryAccountDetailModel NetworkBytes { get; set; }

        public UsageSummaryAccountDetailModel PrimaryStorage { get; set; }

        public UsageSummaryAccountDetailModel SecondaryStorage { get; set; }

        public UsageSummaryAccountLicensingDetailModel Licensing { get; set; }

        public int VirtualMachineCount { get; set; }
    }

}
