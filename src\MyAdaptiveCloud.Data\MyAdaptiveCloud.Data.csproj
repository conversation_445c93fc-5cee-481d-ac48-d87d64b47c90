<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0-preview.3.efcore.9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MyAdaptiveCloud.Core\MyAdaptiveCloud.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <!-- Make sure we include our liquibase files in the publish -->
    <None Include="sql/myadaptivecloud/*" Exclude="sql/**/*.local.properties*" CopyToPublishDirectory="PreserveNewest" />
    <None Include="sql/myadaptivecloudlogs/*" Exclude="sql/**/*.local.properties*" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup>
</Project>
