﻿namespace MyAdaptiveCloud.Services.DTOs.ACMapping
{
    public class ACMappingDTO
    {
        public int? Id { get; set; }
        public Guid AcId { get; set; }
        public string AcName { get; set; }
        public string AcType { get; set; }
        public int? CwCompanyId { get; set; }
        public string CwCompanyName { get; set; }
        public string CwCompanyIdentifier { get; set; }
        public string CwAgreementName { get; set; }
        public int? CwAgreementId { get; set; }
        public bool Enabled { get; set; }
        public bool AcEntityExists { get; set; }
        public DateTimeOffset? BillingStartDate { get; set; }
        public DateTimeOffset? CwBillingStartDate { get; set; }
        public DateTimeOffset? NextInvoiceDate { get; set; }
        public bool HasFutureBillingDate { get; set; }
        public BillingStatusDTO BillingStatus { get; set; }
    }


    public enum BillingStatusDTO
    {
        NewDomain = 0,
        BillingFuture = 1,
        NoBilling = 2,
        Billing = 3
    }
}