﻿namespace MyAdaptiveCloud.Services.Apis.DDoSMitigation.Models
{
    public class Neighbor
    {
        public string Ip { get; set; }
        public int LocalAs { get; set; }
        public int RemoteAs { get; set; }
        public string State { get; set; }
        public string Updown { get; set; }
        public int PrefixesSent { get; set; }
        public int PrefixesSentSimulated { get; set; }
        public int PrefixesReceived { get; set; }
        public int HoldTime { get; set; }
        public int RetryInterval { get; set; }
        public string ScrubPhase1Community { get; set; }
        public string ScrubPhase2Community { get; set; }
        public string ScrubPostCommunity { get; set; }
        public string BlackholeCommunity { get; set; }
    }
}