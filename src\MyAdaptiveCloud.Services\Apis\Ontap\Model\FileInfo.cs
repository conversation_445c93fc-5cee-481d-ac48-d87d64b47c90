namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public enum FileType
{
    file,
    directory,
    blockdev,
    chardev,
    symlink,
    socket,
    fifo,
    stream,
    lun
}

public class FileInfo: OntapObject
{
    public string Path { get; set; }
    public FileType Type { get; set; }
    
    public int UnixPermissions { get; set; }
    
    public FileInfo() {}

    public FileInfo(FileType type, int unixPermissions)
    {
        Type = type;
        UnixPermissions = unixPermissions;
    }

    public override string ToString()
    {
        return $"File: {Name} Path: {Path} Type: {Type}";
    }
}