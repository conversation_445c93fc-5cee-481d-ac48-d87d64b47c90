﻿namespace MyAdaptiveCloud.Services.DTOs.CloudInfra
{
    public class CloudInfraUserContext
    {
        public Guid AccountId { get; set; }

        public string AccountName { get; set; }

        public string ApiKey { get; set; }

        public string ApiUrl { get; set; }

        public string ApiVersion { get; set; }

        public int CpuCustomOfferingMaxValue { get; set; }

        public Guid? DomainId { get; set; }

        public int DiskSizeCustomOfferingMaxValue { get; set; }

        public int DiskSizeCustomOfferingMinValue { get; set; }

        public bool HasMappedDomain { get; set; }

        public int MemoryCustomOfferingMaxValue { get; set; }

        public List<string> Permissions { get; set; }

        public string RoleName { get; set; }

        public string RoleType { get; set; }

        public string SecretKey { get; set; }
    }
}