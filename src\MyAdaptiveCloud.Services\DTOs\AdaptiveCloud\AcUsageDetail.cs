﻿using MyAdaptiveCloud.Data.MyAdaptiveCloud;

namespace MyAdaptiveCloud.Services.DTOs.AdaptiveCloud
{
    public class AcUsageDetail
    {
        public Guid acId { get; set; }
        public string acName { get; set; }
        public double? totalvCPUs { get; set; }
        public double? totalRamGB { get; set; }
        public double? ipaddresses { get; set; }
        public double? networkbytes { get; set; }
        public double? primarystorage { get; set; }
        public double? secondarystorage { get; set; }
        public ICollection<ACUsageComputeRec> computeRecs { get; set; }
        public ICollection<ACUsageLicenseModel> computelicense { get; set; }

    }
}