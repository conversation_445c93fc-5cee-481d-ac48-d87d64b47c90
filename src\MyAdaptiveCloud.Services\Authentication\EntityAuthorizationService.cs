﻿using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Data.Agent;
using MyAdaptiveCloud.Data.Billing;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Apis.ConnectWise;
using MyAdaptiveCloud.Services.Authentication.DTOs;

namespace MyAdaptiveCloud.Services.Authentication
{
    public class EntityAuthorizationService : IEntityAuthorizationService
    {
        private readonly ReadOnlyAgentContext _agentContext;
        private readonly MyAdaptiveCloudContext _myAdaptiveCloudContext;
        private readonly BillingContext _billingContext;
        private readonly IConnectWiseApi _connectWiseApi;

        public EntityAuthorizationService(
            ReadOnlyAgentContext agentContext,
            MyAdaptiveCloudContext myAdaptiveCloudContext,
            BillingContext billingContext,
            IConnectWiseApi connectWiseApi)
        {
            _agentContext = agentContext;
            _myAdaptiveCloudContext = myAdaptiveCloudContext;
            _billingContext = billingContext;
            _connectWiseApi = connectWiseApi;
        }


        public IEnumerable<int> GetDescendantOrganizationsId(int organizationId)
        {
            return _myAdaptiveCloudContext.GetDescendantOrganizations(organizationId).Select(o => o.OrganizationId);
        }

        public async Task<int?> GetActivationKeyOrganizationId(int activationKeyId)
        {
            var activationKeyOrganizationId = await _agentContext.ActivationKey
                .Where(u => u.Id == activationKeyId)
                .Select(a => new int?(a.OrgId))
                .SingleOrDefaultAsync();

            if (activationKeyOrganizationId.HasValue)
            {
                return await _myAdaptiveCloudContext.Organization
                    .Where(o => o.OrganizationId == activationKeyOrganizationId.Value && o.IsActive)
                    .Select(o => new int?(o.OrganizationId))
                    .SingleOrDefaultAsync();
            }
            else
            {
                return null;
            }
        }

        public async Task<int?> GetAdaptiveCloudOrganizationId(string id)
        {
            var guidParsed = Guid.TryParse(id, out Guid guidId);
            return guidParsed
                ? await _myAdaptiveCloudContext.CloudInfraOrganizationMapping
                    .Where(mapping => mapping.DomainId == guidId || mapping.AccountId == guidId)
                    .Select(a => new int?(a.OrganizationId))
                    .SingleOrDefaultAsync()
                : null;
        }

        public async Task<bool> GetCloudInfraMappingIdByOrganizationId(int organizationId)
        {
            return await _myAdaptiveCloudContext.CloudInfraOrganizationMapping
                .AnyAsync(mapping => mapping.OrganizationId == organizationId);
        }

        public async Task<int?> GetAgentOrganizationId(int agentId)
        {
            var agent = await _agentContext.ActiveAgent
                .Where(w => w.AgentId == agentId)
                .Select(a => new int?(a.OrgId))
                .SingleOrDefaultAsync();

            if (agent.HasValue)
            {
                return await _myAdaptiveCloudContext.Organization
                    .Where(o => o.OrganizationId == agent.Value && o.IsActive)
                    .Select(o => new int?(o.OrganizationId))
                    .SingleOrDefaultAsync();
            }
            else
            {
                return null;
            }
        }

        public async Task<IEnumerable<int>> GetAgentsOrganizationId(IEnumerable<int> agentIds)
        {
            var organizationIds = await _agentContext.ActiveAgent
                .Where(w => agentIds.Contains(w.AgentId))
                .Select(a => a.OrgId)
                .Distinct()
                .ToListAsync();

            if (organizationIds != null && organizationIds.Any())
            {
                return await _myAdaptiveCloudContext.Organization
                    .Where(o => organizationIds.Contains(o.OrganizationId) && o.IsActive)
                    .Select(o => o.OrganizationId)
                    .Distinct()
                    .ToListAsync();
            }
            else
            {
                return Array.Empty<int>();
            }
        }

        public async Task<AuthorizePersonDTO> GetPerson(int personId)
        {
            return await _myAdaptiveCloudContext.Person
                .Where(p => p.PersonId == personId)
                .Select(p => new AuthorizePersonDTO { IsApiUser = p.IsApiUser })
                .SingleOrDefaultAsync();
        }

        public async Task<List<int>> GetPersonOrganizationIds(int personId)
        {
            return await _myAdaptiveCloudContext.Member
                .Where(c => c.PersonId == personId)
                .Select(c => c.OrganizationId)
                .ToListAsync();
        }

        public async Task<int?> GetAuthenticationKeyPersonId(int authenticationKeyId)
        {
            return await _myAdaptiveCloudContext.AuthenticationKeys
                .Where(a => a.Id == authenticationKeyId)
                .Select(a => a.PersonId)
                .SingleOrDefaultAsync();
        }

        public async Task<bool> RoleIsAvailableToOrganization(int roleId, int organizationId)
        {
            return (await _myAdaptiveCloudContext.GetAvailableOrgsForRoleWithinOrgHier(roleId, organizationId)).Any(x =>
                x.OrganizationId == organizationId);
        }

        public async Task<bool> RoleIsRestricted(int roleId)
        {
            return await _myAdaptiveCloudContext.Role
                .Where(u => u.RoleId == roleId)
                .Select(r => r.IsRestricted)
                .SingleOrDefaultAsync();
        }

        public async Task<bool> BillingCompanyExists(int companyId)
        {
            return await _billingContext.Company
                .AnyAsync(a => a.CompanyId == companyId);
        }

        /// <inheritdoc/>
        public async Task<int?> GetContactOrganizationId(int userId)
        {
            return await _myAdaptiveCloudContext.Contact
                .Where(c => c.Id == userId)
                .Select(c => new int?(c.OrganizationId))
                .SingleOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<int?> GetUserOrganizationId(int userId)
        {
            return await _myAdaptiveCloudContext.User
                .Where(c => c.Id == userId && !c.Person.IsApiUser)
                .Select(c => new int?(c.OrganizationId))
                .SingleOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<int?> GetApiUserOrganizationId(int apiUserId)
        {
            return await _myAdaptiveCloudContext.User
                .Where(c => c.Id == apiUserId && c.Person.IsApiUser)
                .Select(c => new int?(c.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<bool> OrganizationIsPartner(int organizationId)
        {
            return await _myAdaptiveCloudContext.Organization
                .Where(x => x.OrganizationId == organizationId)
                .Select(x => x.IsPartner)
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetDeviceAlertRuleOrganizationId(int deviceAlertRuleId)
        {
            return await _myAdaptiveCloudContext.DeviceAlertRules
                .Where(dar => dar.DeviceAlertRuleId == deviceAlertRuleId)
                .Select(dar => new int?(dar.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetDeviceFolderOrganizationId(int deviceFolderId)
        {
            return await _myAdaptiveCloudContext.DeviceFolder
                .Where(f => f.FolderId == deviceFolderId)
                .Select(f => new int?(f.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<List<int>> GetDeviceFoldersOrganizationId(List<int> deviceFolderIds)
        {
            return await _myAdaptiveCloudContext.DeviceFolder
                .Where(f => deviceFolderIds.Contains(f.FolderId))
                .Select(f => f.OrganizationId)
                .Distinct()
                .ToListAsync();
        }

        public async Task<List<int>> GetDevicesControlsOrganizationId(List<int> deviceControlIds)
        {
            var agentIds = await _myAdaptiveCloudContext.DeviceControl
                .Where(f => deviceControlIds.Contains(f.DeviceControlId))
                .Select(f => f.DeviceId)
                .Distinct()
                .ToListAsync();

            var orgsIds = await _agentContext.ActiveAgent
                .Where(w => agentIds.Contains(w.AgentId))
                .Select(a => a.OrgId).Distinct().ToListAsync();

            return orgsIds;
        }

        public async Task<int?> GetScheduleDowntimeOrganizationId(int scheduleDowntimeId)
        {
            return await _myAdaptiveCloudContext.ScheduledDowntime
                .Where(sd => sd.ScheduledDowntimeId == scheduleDowntimeId)
                .Select(sd => new int?(sd.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetEscalationChainOrganizationId(int escalationChainId)
        {
            return await _myAdaptiveCloudContext.EscalationChain
                .Where(sd => sd.EscalationChainId == escalationChainId)
                .Select(sd => new int?(sd.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetAdaptiveCloudDriveFileOrganizationId(int fileId)
        {
            return await _myAdaptiveCloudContext.AdaptiveCloudDriveFile
                .Where(f => f.FileAdministrationId == fileId)
                .Select(f => new int?(f.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<AuthorizeAdaptiveCloudDriveFolderDTO> GetAdaptiveCloudDriveFolder(int folderId)
        {
            return await _myAdaptiveCloudContext.AdaptiveCloudDriveFolder
                .Where(f => f.FolderId == folderId)
                .Select(f => new AuthorizeAdaptiveCloudDriveFolderDTO
                    { OrganizationId = f.OrganizationId, IsHidden = f.Hidden })
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetFileServerOrganizationId(int fileServerId)
        {
            return await _myAdaptiveCloudContext.FileServers
                .Where(f => f.Id == fileServerId)
                .Select(f => new int?(f.OrganizationId))
                .SingleOrDefaultAsync();
        }


        public async Task<int?> GetInvoiceOrganizationId(int invoiceId)
        {
            var invoice = await _connectWiseApi.GetInvoice(invoiceId);
            if (invoice.Company != null)
            {
                return await _myAdaptiveCloudContext.ConnectWiseOrganizationMapping
                    .Where(mapping => mapping.CompanyId == invoice.Company.Id)
                    .Select(a => new int?(a.OrganizationId))
                    .SingleOrDefaultAsync();
            }

            return null;
        }

        public async Task<int?> GetNotificationOrganizationId(int notificationId)
        {
            return await _myAdaptiveCloudContext.Notification
                .Where(n => n.NotificationId == notificationId)
                .Select(n => new int?(n.OrganizationId))
                .SingleOrDefaultAsync();
        }

        /// <inheritdoc />
        public bool IsOrganizationWithinOrganizationHierarchy(int organizationId, int descendantOrganizationId)
        {
            var descendantOrganizations = _myAdaptiveCloudContext.OrganizationHierarchy
                .FromSql($"CALL OrgHierarchy({organizationId})").AsNoTracking().ToList();
            return descendantOrganizations.Select(o => o.OrganizationId).Contains(descendantOrganizationId);
        }

        public async Task<int?> GetPolicyOrganizationId(int policyId)
        {
            return await _myAdaptiveCloudContext.Policy
                .Where(p => p.PolicyId == policyId)
                .Select(n => new int?(n.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<AuthorizeRoleDTO> GetRoleOrganizationId(int roleId)
        {
            return await _myAdaptiveCloudContext.Role
                .Where(r => r.RoleId == roleId)
                .Select(r => new AuthorizeRoleDTO { OrganizationId = r.OrganizationId, IsRestricted = r.IsRestricted })
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetScheduleOrganizationId(int scheduleId)
        {
            return await _myAdaptiveCloudContext.Schedule
                .Where(s => s.ScheduleId == scheduleId)
                .Select(s => new int?(s.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetServiceOrganizationId(int serviceId)
        {
            return await _myAdaptiveCloudContext.Service
                .Where(s => s.ServiceId == serviceId)
                .Select(s => new int?(s.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetUserRoleOrganizationId(int userRoleId)
        {
            return await _myAdaptiveCloudContext.UserRole
                .Where(u => u.UserRoleId == userRoleId)
                .Include(u => u.User)
                .Select(u => new int?(u.User.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<IEnumerable<int>> GetUserRoleOrganizationIds(List<int> userRoleIds)
        {
            return await _myAdaptiveCloudContext.UserRole
                .Where(u => userRoleIds.Contains(u.UserRoleId))
                .Include(u => u.User)
                .Select(u => u.User.OrganizationId)
                .Distinct()
                .ToListAsync();
        }

        public async Task<bool> InvitationCodeExists(string code)
        {
            return await _myAdaptiveCloudContext.InvitationCodes
                .AnyAsync(f => f.Code == code);
        }

        public async Task<AuthorizeAgentActionByVersionServiceDTO> AgentActionByVersionService(int agentId)
        {
            var agentOrganizationId = await _agentContext.ActiveAgent
                .Where(a => a.AgentId == agentId)
                .Select(a => new int?(a.OrgId))
                .SingleOrDefaultAsync();

            if (agentOrganizationId.HasValue)
            {
                var organizationIsActive = await _myAdaptiveCloudContext.Organization
                    .AnyAsync(o => o.OrganizationId == agentOrganizationId && o.IsActive);

                if (organizationIsActive)
                {
                    return await _agentContext.ActiveAgent
                        .Where(a => a.AgentId == agentId)
                        .Include(a => a.AgentService)
                        .Select(a => new AuthorizeAgentActionByVersionServiceDTO
                        {
                            AgentServiceBuild = a.AgentService.Build,
                            AgentServiceMajor = a.AgentService.Major,
                            AgentServiceMinor = a.AgentService.Minor
                        })
                        .SingleOrDefaultAsync();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public bool HasUserRolesByOrg(int userId, int organizationId, bool includeUnapprovedRoles)
        {
            var userRoles = _myAdaptiveCloudContext.GetUserRolesByOrg(userId, organizationId);
            if (!includeUnapprovedRoles)
            {
                return userRoles.Any(x => x.IsApproved);
            }

            return userRoles.Any();
        }

        public async Task<bool> OrganizationExists(int organizationId, bool includeInactive)
        {
            var query = _myAdaptiveCloudContext.Organization.Where(o => o.OrganizationId == organizationId);
            if (!includeInactive)
            {
                query = query.Where(o => o.IsActive);
            }

            return await query.AnyAsync();
        }

        public async Task<int?> GetDeviceControlOrganizationId(int deviceControlId)
        {
            var deviceControlDeviceId = await _myAdaptiveCloudContext.DeviceControl
                .Where(dc => dc.DeviceControlId == deviceControlId)
                .Select(dc => new int?(dc.DeviceId))
                .SingleOrDefaultAsync();

            if (deviceControlDeviceId.HasValue)
            {
                var agent = await _agentContext.ActiveAgent
                    .Where(w => w.AgentId == deviceControlDeviceId.Value)
                    .Select(a => new int?(a.OrgId))
                    .SingleOrDefaultAsync();

                if (agent.HasValue)
                {
                    return await _myAdaptiveCloudContext.Organization
                        .Where(o => o.OrganizationId == agent.Value && o.IsActive)
                        .Select(o => new int?(o.OrganizationId))
                        .SingleOrDefaultAsync();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public async Task<bool> HasTotalTechService(List<int> serviceIds, int organizationId)
        {
            return await _myAdaptiveCloudContext.ServiceOrganization
                .AnyAsync(so => serviceIds.Contains(so.ServiceId) && so.OrganizationId == organizationId);
        }

        public async Task<bool> DocumentIsInTotalTechFolder(int documentId, int folderId)
        {
            return await _myAdaptiveCloudContext
                .AdaptiveCloudDriveFile
                .AnyAsync(f => f.FileAdministrationId == documentId && f.FolderId == folderId);
        }

        public async Task<bool> DocumentIsInPartnerResourceHubFolderHierarchy(int documentId, int folderId)
        {
            var folderIds = (await _myAdaptiveCloudContext.GetPartnerResourceHubFolders(folderId)).Select(f => f.FolderId);
            return await _myAdaptiveCloudContext
                .AdaptiveCloudDriveFile
                .AnyAsync(f => f.FileAdministrationId == documentId && folderIds.Contains(f.FolderId));
        }

        public async Task<int?> GetOrganizationIdInvitationCode(int invitationCodeId)
        {
            return await _myAdaptiveCloudContext.InvitationCodes
                .Where(s => s.InvitationCodeId == invitationCodeId)
                .Select(s => new int?(s.OrganizationId))
                .SingleOrDefaultAsync();
        }

        /// <inheritdoc/>
        public Task<bool> IsDeviceControlledByUserWithRoles(int agentId, int userId, DeviceControlRoleEnum[] deviceControlRoles)
        {
            return _myAdaptiveCloudContext.DeviceControl.AnyAsync(deviceControl =>
                deviceControl.DeviceId == agentId
                && deviceControlRoles.Contains((DeviceControlRoleEnum)deviceControl.Role.DeviceControlRoleId)
                && _myAdaptiveCloudContext.Person.Where(p => p.PersonId == userId).Select(p => p.Email).Contains(deviceControl.UserEmail));
        }

        public async Task<bool> ValidateUnsubscribeMessage(string subscriberEmail, string messageQueueItemMessageId)
        {
            return await _myAdaptiveCloudContext.MessageQueue.AnyAsync(mq => mq.Recipient == subscriberEmail && mq.MessageId == messageQueueItemMessageId);
        }

        public async Task<bool> HasPersonAccessToService(int organizationId, int serviceId, int personId)
        {
            var personIsMember = await _myAdaptiveCloudContext.Member.AnyAsync(m => m.PersonId == personId && m.OrganizationId == organizationId);

            if (!personIsMember)
            {
                return false;
            }

            return await _myAdaptiveCloudContext.ServiceOrganization
                .Where(so => so.ServiceId == serviceId && so.OrganizationId == organizationId).AnyAsync();
        }

        public async Task<bool> IsOrganizationInactive(int organizationId)
        {
            return await _myAdaptiveCloudContext.Organization.IgnoreQueryFilters()
                .Where(o => o.OrganizationId == organizationId)
                .Select(o => !o.IsActive)
                .SingleOrDefaultAsync();
        }

        public async Task<int?> GetReportRequestOrganizationId(int reportId)
        {
            return await _myAdaptiveCloudContext.ReportRequest
                .Where(s => s.Id == reportId)
                .Select(s => new int?(s.OrganizationId))
                .SingleOrDefaultAsync();
        }

        public async Task<bool> HasReportExecutionId(int reportExecutionId)
        {
            return await _myAdaptiveCloudContext.ReportExecution
                .AnyAsync(s => s.Id == reportExecutionId);
        }
    }
}