﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Reports;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Reports.Execution;
using MyAdaptiveCloud.Services.DTOs.Report;
using MyAdaptiveCloud.Services.DTOs.Report.Creation;

namespace MyAdaptiveCloud.Services.AutoMapper.Reports
{
    public class ReportsMapperProfile : Profile
    {
        public ReportsMapperProfile()
        {
            CreateMap<ReportRequest, ReportRequestDTO>()
                .ForMember(dest => dest.Organizations, opt => opt.Ignore())
                .ForMember(dest => dest.RecipientsCount, opt => opt.Ignore())
                .ForMember(dest => dest.CanView, opt => opt.Ignore())
                .ForMember(dest => dest.CanCreate, opt => opt.Ignore())
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByEmail, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.MapFrom(r => r.Id))
                .ForMember(dest => dest.ReportSchedule,
                    opt => opt.MapFrom(r => r.Schedule != null ? ReportScheduleDTO.MapToReportScheduleDTO(r.Schedule) : null))
                .ForMember(dest => dest.NextScheduledExecution,
                    opt => opt.MapFrom(r => r.NextScheduledExecution.HasValue ? (DateTimeOffset?)new DateTimeOffset(r.NextScheduledExecution.Value, TimeSpan.Zero) : null));

            CreateMap<ReportRequestCreationDTO, PatchComplianceReportRequest>()
                .ForMember(dest => dest.Schedule, opt => opt.Ignore())
                .ForMember(dest => dest.Executions, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByPerson, opt => opt.Ignore())
                .ForMember(dest => dest.ReportType, opt => opt.Ignore());

            CreateMap<ReportRequestCreationDTO, PatchAuditReportRequest>()
                .ForMember(dest => dest.Schedule, opt => opt.Ignore())
                .ForMember(dest => dest.Executions, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByPerson, opt => opt.Ignore())
                .ForMember(dest => dest.ReportType, opt => opt.Ignore());

            CreateMap<PatchComplianceReportRequestCreationDTO, PatchComplianceReportRequest>()
                .ForMember(dest => dest.Schedule, opt => opt.Ignore())
                .ForMember(dest => dest.Executions, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByPerson, opt => opt.Ignore())
                .ForMember(dest => dest.ReportType, opt => opt.Ignore());

            CreateMap<ReportExecution, ReportExecutionDTO>()
                .ConstructUsing(x => new(0, DateTime.UtcNow, string.Empty, ReportExecutionMode.Scheduled, 1));

            CreateMap<ReportExecutionDTO, ReportExecution>()
                .ForMember(dest => dest.ReportRequest, opt => opt.Ignore())
                .ForMember(dest => dest.ExecutedBy, opt => opt.MapFrom(x => x.ExecutedBy))
                .ForMember(dest => dest.CreatedByPerson, opt => opt.Ignore());


            CreateMap<ReportExecution, DownloadReportDTO>()
                .ForMember(dest => dest.ReportName, opt => opt.Ignore())
                .ForMember(dest => dest.ReportFilePath, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationName, opt => opt.Ignore());
        }
    }
}