using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;


public class OntapObject
{
    [JsonPropertyName("_links")] public Links Links { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public Guid Uuid { get; set; }
    public string Name { get; set; }
    public OntapObject() {}

    public OntapObject(Guid uuid)
    {
        Uuid = uuid;
    }

    public OntapObject(string name, Guid uuid)
    {
        Name = name;
        Uuid = uuid;
    }

    public OntapObject(string name)
    {
        Name = name;
    }
}

public class Links
{
    public Link Self { get; set; }
}

public class Link
{
    public string Href { get; set; }
}
