﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisContact
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }
        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }
        [JsonPropertyName("types")]
        public List<string> Types { get; set; }
        [JsonPropertyName("email")]
        public string Email { get; set; }
        [JsonPropertyName("address1")]
        public string Address1 { get; set; }
        [JsonPropertyName("address2")]
        public string Address2 { get; set; }
        [JsonPropertyName("deleted_at")]
        public string DeletedAt { get; set; }
        [JsonPropertyName("firstname")]
        public string Firstname { get; set; }
        [JsonPropertyName("lastname")]
        public string Lastname { get; set; }
        #nullable enable
        [JsonPropertyName("country")]
        public string? Country { get; set; }
        [JsonPropertyName("state")]
        public string? State { get; set; }
        [JsonPropertyName("zipcode")]
        public string? Zipcode { get; set; }
        [JsonPropertyName("city")]
        public string? City { get; set; }
        [JsonPropertyName("phone")]
        public string? Phone { get; set; }
        [JsonPropertyName("title")]
        public string? Title { get; set; }
        [JsonPropertyName("website")]
        public string? Website { get; set; }
        [JsonPropertyName("industry")]
        public string? Industry { get; set; }
        [JsonPropertyName("organization_size")]
        public string? OrganizationSize { get; set; }
        [JsonPropertyName("email_confirmed")]
        public bool? EmailConfirmed { get; set; }
        [JsonPropertyName("aan")]
        public string? Aan { get; set; }
        #nullable disable
    }
}
