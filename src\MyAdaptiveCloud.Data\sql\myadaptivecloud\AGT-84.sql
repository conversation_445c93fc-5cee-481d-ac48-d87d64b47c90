-- liquibase formatted sql

-- changeset gnanaziashvili:286f25bf-7699-4ee6-9dec-9b5f8ad6ba92 context:"main"
CALL AddConfigurationCategory('AgentRemote Api');
CALL SetConfigurationValue('AgentR<PERSON>ote Api', 'AgentRemoteBaseUrl', '', 'input', 0);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteApiKey', '', 'input', 0);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteApiSecret', '', 'input', 1);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteDisplayBaseUrl', '', 'input', 0);

-- changeset gnanaziashvili:18d2e818-6b75-4467-84ec-515c4cc1adc7 context:dev,test
-- Set setting in dev and test to point to lab
CALL SetConfigurationValue('AgentRem<PERSON> Api', 'Agent<PERSON>emote<PERSON>piKey', 'b1a7f908-4ada-4441-9b09-05bf5385ae9b', 'input', 0);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteApiSecret', 'XH4nrr2S3CtxYBbpr6p92JQ2nWjVV7XKbu3xTMPyEXzqrBpLYCxMyEkZ6W6ByvXv24fYLEKz7LtWqxFwgRxXag77bR9cYNV5hE2vtzhDfMNsRhn3k6dEV4ATMjN8bTwt', 'input', 1);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteBaseUrl', 'https://remote-api.ippathways.us/api', 'input', 0);
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteDisplayBaseUrl', 'https://remote-api.ippathways.us/RemoteDisplay/', 'input', 0);

-- changeset gnanaziashvili:2917e700-4f35-41f6-b5b6-863c8936b68a context:"main"
-- comment: Adding AgentRemoteDisplayConnectionStatusCheckInterval
CALL SetConfigurationValue('AgentRemote Api', 'AgentRemoteDisplayConnectionStatusCheckInterval', '17', 'input', 0);