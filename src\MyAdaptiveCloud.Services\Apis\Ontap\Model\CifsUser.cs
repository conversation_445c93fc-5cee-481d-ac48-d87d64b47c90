namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class CifsUser: OntapObject
{
    public bool AccountDisabled { get; set; }
    public string Description { get; set; }
    public string FullName { get; set; }
    public string Password { get; set; }
    public string Sid { get; set; }
    public Svm Svm { get; set; }
    public List<CifsGroup> Membership { get; set; }
    
    public CifsUser() {}

    public CifsUser(string name, string password, string fullName, string description, bool disabled, Guid svm, string svmName)
    {
        Name = name;
        Password = password;
        FullName = fullName;
        Description = description;
        AccountDisabled = disabled;
        Svm = new Svm(svm);
        Svm.Name = svmName;
    }

    public CifsUser(string password)
    {
        Password = password;
    }

    public CifsUser(string password, string name, string fullName, string description, bool disabled)
    {
        Name = name;
        Password = password;
        FullName = fullName;
        Description = description;
        AccountDisabled = disabled;
    }

    public CifsUser(string name, string fullName, string description, bool disabled)
    {
        Name = name;
        FullName = fullName;
        Description = description;
        AccountDisabled = disabled;
    }

    public override string ToString()
    {
        return $"User: {Name} Sid: {Sid}";
    }
}