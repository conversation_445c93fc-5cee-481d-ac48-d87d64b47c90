using MyAdaptiveCloud.Services.Apis.KeyCloak.Model;
using KeyCloakAuthenticatorProviders = MyAdaptiveCloud.Services.Apis.KeyCloak.Model.Credential;
using MyAdaptiveCloud.Services.Requests.Profile;

namespace MyAdaptiveCloud.Services.Apis.KeyCloak
{
    public interface IKeyCloakApi
    {
        Task<User> GetUser(string keycloakUserId);

        Task<HttpResponseMessage> UpdateUser(User keycloakUser);

        Task SetUserAttributes(string keycloakUserId, Dictionary<string, List<string>> attributes);

        Task<User> GetUserByUsername(string email);

        Task<List<User>> GetUsersList();

        Task<List<KeyCloakAuthenticatorProviders>> GetOtpAuthenticatorProviders(string keyCloakUserId);

        Task DeleteAuthenticatorProvider(string userId, string credentialId);

        Task UpdateAuthenticatorProvider(string keyCloakUserId, KeyCloakAuthenticatorProviders authenticatorProvider);

        Task<bool> ResetPassword(string keyCloakUserId, string newPassword);

        Task<PasswordPolicyRequest> GetPasswordPolicy();

        Task DeleteUser(string keyCloakUserId);
    }
}