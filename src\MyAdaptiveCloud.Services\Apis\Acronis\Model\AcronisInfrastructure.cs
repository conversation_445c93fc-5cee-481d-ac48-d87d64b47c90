﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisInfrastructure
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }
        [JsonPropertyName("owner_tenant_id")]
        public Guid OwnerTenantId { get; set; }
        [JsonPropertyName("location_id")]
        public Guid LocationId { get; set; }
        [JsonPropertyName("platform_owned")]
        public bool PlatformOwned { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
        [JsonPropertyName("url")]
        public string Url { get; set; }
        [JsonPropertyName("content_url")]
        public string ContentUrl { get; set; }
        [JsonPropertyName("content_mobile_url")]
        public string ContentMobileUrl { get; set; }
        [JsonPropertyName("backend_type")]
        public string BackendType { get; set; }
        #nullable enable
        [JsonPropertyName("readonly")]
        public bool? Readonly { get; set; }
        #nullable disable
        [Json<PERSON>ropertyName("version")]
        public int Version { get; set; }
        [Json<PERSON>ropertyName("capabilities")]
        public List<string> Capabilities { get; set; }
    }
}
