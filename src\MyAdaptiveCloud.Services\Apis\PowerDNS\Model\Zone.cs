namespace MyAdaptiveCloud.Services.Apis.PowerDNS.Model
{
    public class ZoneLite
    {
        public List<ResourceRecordSet> rrsets { get; set; }
    }

    public class Zone
    {
        public string id { get; set; }
        public string name { get; set; } ///< MUST have a trailing dot. An example is 'adaptivecloud.com.'
        public string type { get; set; }
        public string url { get; set; }
        public string kind { get; set; } ///< Native, Master, Slave
        public List<ResourceRecordSet> rrsets { get; set; }
        public int serial { get; set; }
        public int notified_serial { get; set; }
        public int edited_serial { get; set; }
        public List<string> masters { get; set; }
        public bool dnssec { get; set; }
        public string nsec3param { get; set; }
        public bool nsec3narrow { get; set; }
        public bool presigned { get; set; }
        public string soa_edit { get; set; }
        public string soa_edit_api { get; set; }
        public bool api_rectify { get; set; }
        public string zone { get; set; }
        public string account { get; set; }
        public List<string> nameservers { get; set; }
        public List<string> master_tsig_key_ids { get; set; }
        public List<string> slave_tsig_key_ids { get; set; }
    }

    public class ResourceRecordSet
    {
        public string name { get; set; } ///< MUST have a trailing dot
        public string type { get; set; } ///< e.g. A, PTR, CNAME, MX
        public int? ttl { get; set; }
        public string changetype { get; set; }
        public List<Record> records { get; set; }
        public List<Comment> comments { get; set; }
    }

    public class Record
    {
        public string content { get; set; }
        public bool disabled { get; set; }
    }

    public class Comment
    {
        public string content { get; set; }
        public string account { get; set; }
        public int modified_at { get; set; }
    }
}