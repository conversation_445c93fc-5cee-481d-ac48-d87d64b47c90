﻿namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class AgentDTO
    {
        public int AgentId { get; set; }

        public int OrgId { get; set; }

        public string OrganizationName { get; set; }

        public string Hostname { get; set; }

        public string Description { get; set; }

        public int? FolderId { get; set; }

        public string Name => $"{Hostname}";

        public string Path { get; set; }
        public Guid AgentUuid { get; set; }
        public string Ip { get; set; }
        public string OperatingSystem { get; set; }
        public DateTimeOffset? HeartbeatTs { get; set; }

        public bool CloudStackVM { get; set; }
    }
}