﻿namespace MyAdaptiveCloud.Services.DTOs.Agent.AgentManagement
{
    public class ServiceVersionListDTO
    {
        public string Version { get; set; }
        public DateTime ReleaseDate { get; set; }
        public int ServiceId { get; set; }
        public string Name { get; set; }
        public bool CanDelete { get; set; }
        public bool CanMarkObsolete { get; set; }
        public bool IsObsolete { get; set; }
        public string ChecksumType { get; set; }
        public string Checksum { get; set; }
        public int AgentCount { get; set; }
        public string ReleaseTag { get; set; }
        public ServiceTypeDTO ServiceType { get; set; }
    }
}