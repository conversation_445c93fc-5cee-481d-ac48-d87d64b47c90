using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Apis.Ontap.Model;
using MyAdaptiveCloud.Services.Services;


namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class OntapApi : IOntapApi
{
    private readonly HttpClient _client;
    private readonly string _authHeader;
    public string AggregateName { get; }
    public string BasePortName { get; }

    private Dictionary<Type, TypeApi> _apis;

    public OntapApi(HttpClient client, IConfigurationService configurationService, MyAdaptiveCloudContext context)
    {
        _client = client;
        var config = configurationService.GetOntapConfiguration().GetAwaiter().GetResult();

        if (!String.IsNullOrWhiteSpace(config.BaseUrl))
        {
            client.BaseAddress = new Uri(config.BaseUrl);
        }

        _authHeader = config.AuthHeader;
        AggregateName = config.AggregateName;
        BasePortName = config.BasePortName;
        _apis = new Dictionary<Type, TypeApi>();
    }

    private T GetApi<T>(Type type, Func<T> cons) where T : TypeApi
    {
        if (_apis.TryGetValue(type, out var api))
        {
            return (T)api;
        }

        var typeApi = cons();
        _apis.Add(type, typeApi);
        return typeApi;
    }

    public PortApi Port()
    {
        return GetApi(typeof(PortApi), () => new PortApi(this));
    }

    public NodeApi Node()
    {
        return GetApi(typeof(NodeApi), () => new NodeApi(this));
    }

    public IpspaceApi Ipspace()
    {
        return GetApi(typeof(IpspaceApi), () => new IpspaceApi(this));
    }

    public BroadcastDomainApi BroadcastDomain()
    {
        return GetApi(typeof(BroadcastDomainApi), () => new BroadcastDomainApi(this));
    }

    public SvmApi Svm()
    {
        return GetApi(typeof(SvmApi), () => new SvmApi(this));
    }

    public CifsApi Cifs()
    {
        return GetApi(typeof(CifsApi), () => new CifsApi(this));
    }

    public StorageApi Storage()
    {
        return GetApi(typeof(StorageApi), () => new StorageApi(this));
    }

    public async Task<SvmNfs> SetupNfs(Guid svm)
    {
        var body = new SvmNfs(svm);
        return await CreateResource(new UrlBuilder("/protocols/nfs/services"), body);
    }

    public async Task DestroyNfs(Guid svm)
    {
        var body = new SvmNfs(svm);
        await DeleteResource(new UrlBuilder("/protocols/nfs/services/%svm%").AddPathParam("svm", svm));
    }

    public async Task<Job> GetJob(Guid job)
    {
        var url = new UrlBuilder("/cluster/jobs/%uuid%")
            .AddPathParam("uuid", job.ToString())
            .AddFields("*");
        return await GetResource<Job>(url);
    }

    public async Task<Job> WaitOnJob(Job job)
    {
        if (job == null)
        {
            return job;
        }

        return await WaitOnJob(job.Uuid);
    }

    public async Task<Job> WaitOnJob(Guid job)
    {
        var jobDetails = await GetJob(job);
        var start = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        while (!jobDetails.IsDone() && !jobDetails.IsPaused() && DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - start < 60 * 1000)
        {
            // Console.WriteLine("Waiting on job");
            Thread.Sleep(1000);
            jobDetails = await GetJob(job);
        }

        return jobDetails;
    }

    public async Task<List<T>> GetResources<T>(UrlBuilder url)
    {
        var response = await ExecuteGetRequest(url);
        // Console.WriteLine(await response.Content.ReadAsStringAsync());
        var records = await GetDeserializedResponse<ListResponse<T>>(response);
        return records.Records;
    }

    public async Task<T> GetResource<T>(UrlBuilder url) where T : new()
    {
        var response = await ExecuteGetRequest(url);
        // Console.WriteLine(await response.Content.ReadAsStringAsync());
        var record = await GetDeserializedResponse<T>(response);
        return record;
    }

    public async Task<T> SearchResource<T>(UrlBuilder url) where T : class
    {
        var resources = await GetResources<T>(url);
        if (resources.Count > 0)
        {
            return resources.First();
        }

        return null;
    }

    public async Task<T> CreateResource<T>(UrlBuilder url, T body)
    {
        url.AddQueryParam("return_records", "true");
        var response = await ExecutePostRequest(url, body);
        // Console.WriteLine(await response.Content.ReadAsStringAsync());
        var returnedRecords = await GetDeserializedResponse<ListResponse<T>>(response);
        if (returnedRecords.NumRecords == 0)
        {
            throw new OntapApiException("Unable to create resource");
        }

        return returnedRecords.Records.First();
    }

    public async Task<ListResponse<T>> CreateResourceAsync<T>(UrlBuilder url, T body)
    {
        url.AddQueryParam("return_records", "true");
        var response = await ExecutePostRequest(url, body);
        // Console.WriteLine(await response.Content.ReadAsStringAsync());
        return await GetDeserializedResponse<ListResponse<T>>(response);
    }

    public async Task DeleteResource(UrlBuilder url)
    {
        var success = (await ExecuteDeleteRequest(url)).IsSuccessStatusCode;
        if (!success)
        {
            throw new OntapApiException($"Unable to delete resource at url {url.Render()}");
        }
    }

    public async Task<Job> DeleteResourceAsync(UrlBuilder url)
    {
        var response = await ExecuteDeleteRequest(url);
        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException($"Unable to delete resource at url {url.Render()}");
        }

        var responseContent = await GetDeserializedResponse<ListResponse<OntapObject>>(response);
        return responseContent.Job;
    }

    public async Task<Job> DeleteResourceAsync<T>(UrlBuilder url, T body)
    {
        var response = await ExecuteDeleteRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            throw new OntapApiException($"Unable to delete resource at url {url.Render()} Message: {content}");
        }

        var responseContent = await GetDeserializedResponse<ListResponse<OntapObject>>(response);
        return responseContent.Job;
    }

    public async Task PatchResource<T>(UrlBuilder url, T body)
    {
        var response = await ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to patch resource at url {url.Render()} Message: {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task<HttpResponseMessage> ExecuteGetRequest(UrlBuilder url)
    {
        var relativeUriString = PrepUrl(url);
        using var requestMessage = GetHttpWithAuth(relativeUriString, HttpMethod.Get);

        return await _client.SendAsync(requestMessage);
    }

    public async Task<HttpResponseMessage> ExecutePostRequest<T>(UrlBuilder url, T body)
    {
        string relativeUriString = PrepUrl(url);
        var jsonBody = SerializeJsonBody(body);
        // Console.WriteLine("Content: " + jsonBody);
        var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
        using var requestMessage = GetHttpWithAuth(relativeUriString, HttpMethod.Post);
        requestMessage.Content = content;
        HttpResponseMessage response = await _client.SendAsync(requestMessage);
        return response;
    }

    public async Task<HttpResponseMessage> ExecutePatchRequest<T>(UrlBuilder url, T body)
    {
        string relativeUriString = PrepUrl(url);
        var jsonBody = SerializeJsonBody(body);
        var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
        using var requestMessage = GetHttpWithAuth(relativeUriString, HttpMethod.Patch);
        requestMessage.Content = content;
        HttpResponseMessage response = await _client.SendAsync(requestMessage);
        return response;
    }

    public async Task<HttpResponseMessage> ExecuteDeleteRequest(UrlBuilder url)
    {
        return await ExecuteDeleteRequest<Object>(url, null);
    }

    public async Task<HttpResponseMessage> ExecuteDeleteRequest<T>(UrlBuilder url, T body)
    {
        string relativeUriString = PrepUrl(url);
        using var requestMessage = GetHttpWithAuth(relativeUriString, HttpMethod.Delete);
        if (body != null)
        {
            var jsonBody = SerializeJsonBody(body);
            var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
            requestMessage.Content = content;
        }

        HttpResponseMessage response = await _client.SendAsync(requestMessage);
        return response;
    }

    private HttpRequestMessage GetHttpWithAuth(string relativeUriString, HttpMethod httpMethod)
    {
        var requestMessage = new HttpRequestMessage(httpMethod, relativeUriString);
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Basic", _authHeader);
        return requestMessage;
    }

    public async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
    {
        if (!response.IsSuccessStatusCode && response.StatusCode != System.Net.HttpStatusCode.NotFound)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            var message = content.Error.Message ?? "An error has occurred. Please contact your administrator.";
            string capitalizedMessage = char.ToUpper(message[0]) + message.Substring(1);

            throw new OntapApiException(capitalizedMessage);
        }


        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return new T();
        }

        var options = GetJsonOptions();
        return await JsonSerializer.DeserializeAsync<T>(await response.Content.ReadAsStreamAsync(), options);
    }

    private static string SerializeJsonBody<T>(T body)
    {
        return JsonSerializer.Serialize(body, GetJsonOptions());
    }

    private static JsonSerializerOptions GetJsonOptions()
    {
        return new JsonSerializerOptions()
        {
            PropertyNamingPolicy = SnakeCaseNamingPolicy.Instance,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters =
            {
                new JsonStringEnumConverter(SnakeCaseNamingPolicy.Instance),
            }
        };
    }

    private string PrepUrl(UrlBuilder url)
    {
        return _client.BaseAddress.AbsolutePath + url.Render();
    }
}

public class SnakeCaseNamingPolicy : JsonNamingPolicy
{
    public static SnakeCaseNamingPolicy Instance { get; } = new SnakeCaseNamingPolicy();

    public override string ConvertName(string name)
    {
        return string.Concat(name.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString())).ToLower();
    }
}