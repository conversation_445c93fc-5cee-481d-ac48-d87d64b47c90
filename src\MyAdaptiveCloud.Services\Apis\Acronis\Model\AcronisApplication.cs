﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisApplication
    {
        [JsonPropertyName("type")]
        public string Type { get; set; }
        [JsonPropertyName("api_base_url")]
        public string ApiBaseURL { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
        [JsonPropertyName("id")]
        public string Id { get; set; }
        [JsonPropertyName("usages")]
        public List<string> Usages { get; set; }
    }
}
