﻿using MyAdaptiveCloud.Services.Apis.DDoSMitigation.Models;
using MyAdaptiveCloud.Services.Apis.DDoSMitigation.Responses;
using MyAdaptiveCloud.Services.Requests.DDoSMitigation;

namespace MyAdaptiveCloud.Services.Apis.DDoSMitigation
{
    public interface IDDoSMitigationApiService
    {
        Task<List<Bgp>> GetBGP();
        Task<List<Bgp>> GetAdvertisedBGP();
        Task<List<Blackhole>> GetBlackholeStatus();
        Task<List<Neighbor>> GetNeighbors();
        Task<Health> GetHealth();
        Task<List<TaskDetail>> GetTasks();
        Task<TaskDetail> GetTask(string taskId);
        Task<List<Scrub>> GetScrubStatus();
        Task<DDoSMitigationTaskResponse> StartBlackhole(DdosStartRequest request);
        Task<DDoSMitigationTaskResponse> StopBlackhole(DdosStopRequest request);
        Task<DDoSMitigationTaskResponse> StartScrub(DdosStartRequest request);
        Task<DDoSMitigationTaskResponse> StopScrub(DdosStopRequest request);
        Task<MitigationValidation> ValidateScrub(string cidr);
        Task<MitigationValidation> ValidateBlackhole(string cidr);
    }
}