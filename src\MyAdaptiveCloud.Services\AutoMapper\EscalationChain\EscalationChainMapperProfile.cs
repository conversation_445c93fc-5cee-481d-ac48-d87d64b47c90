﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.EscalationChains;
using MyAdaptiveCloud.Services.DTOs.EscalationChain;

namespace MyAdaptiveCloud.Services.AutoMapper.EscalationChain
{
    public class EscalationChainMapperProfile : Profile
    {
        public EscalationChainMapperProfile()
        {
            CreateMap<EscalationChainDetail, EscalationChainDetailDTO>()
                   .ForMember(dest => dest.UserName, opt => opt.Ignore());

            CreateMap<EscalationChainDetailDTO, EscalationChainDetail>()
                .ForMember(dest => dest.EscalationChainId, opt => opt.Ignore());

            CreateMap<Data.MyAdaptiveCloud.EscalationChains.EscalationChain, EscalationChainDTO>()
                .ForMember(dest => dest.AlertIntervalMinutes, opt => opt.MapFrom(src => src.AlertIntervalMinutes))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details.OrderBy(d => d.Stage)));

            CreateMap<EscalationChainDTO, Data.MyAdaptiveCloud.EscalationChains.EscalationChain>()
                .ForMember(dest => dest.AlertIntervalMinutes, opt => opt.MapFrom(src => src.AlertIntervalMinutes))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details))
                .ForMember(dest => dest.OrganizationId, opt => opt.Ignore())
                .ForMember(dest => dest.Organization, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.EscalationChainId, opt => opt.Ignore());

        }
    }
}
