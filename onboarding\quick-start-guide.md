# MyAdaptiveCloud Quick Start Guide

Get up and running with the MyAdaptiveCloud development environment in under 30 minutes.

## 📋 Prerequisites

Install the following software before proceeding:

### Required Software
- **.NET 9.0 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Node.js 18+** - [Download here](https://nodejs.org/) (LTS version recommended)
- **MySQL/MariaDB** - [MySQL](https://dev.mysql.com/downloads/) or [MariaDB](https://mariadb.org/download/)
- **Git** - [Download here](https://git-scm.com/downloads)

### Optional but Recommended
- **Visual Studio Code** - [Download here](https://code.visualstudio.com/)
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop) (for containerized development)
- **MySQL Workbench** - [Download here](https://dev.mysql.com/downloads/workbench/) (database management)

### Verify Installations
```bash
# Check versions
dotnet --version          # Should be 9.0.x
node --version            # Should be 18.x or higher
npm --version             # Should be 9.x or higher
mysql --version           # Should be 8.0+ or MariaDB 10.11+
git --version             # Any recent version
```

## 🗄️ Database Setup

### 1. Start MySQL/MariaDB Service
```bash
# Windows (if installed as service)
net start mysql

# macOS (using Homebrew)
brew services start mysql
# or
brew services start mariadb

# Linux (systemd)
sudo systemctl start mysql
# or
sudo systemctl start mariadb
```

### 2. Create Development Databases
```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create databases
CREATE DATABASE myadaptivecloud_dev;
CREATE DATABASE acagent_dev;
CREATE DATABASE myadaptivecloudlogs_dev;

-- Create development user (optional but recommended)
CREATE USER 'myac_dev'@'localhost' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON myadaptivecloud_dev.* TO 'myac_dev'@'localhost';
GRANT ALL PRIVILEGES ON acagent_dev.* TO 'myac_dev'@'localhost';
GRANT ALL PRIVILEGES ON myadaptivecloudlogs_dev.* TO 'myac_dev'@'localhost';
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

### 3. Verify Database Creation
```bash
# List databases
mysql -u root -p -e "SHOW DATABASES;"

# Should see:
# myadaptivecloud_dev
# acagent_dev
# myadaptivecloudlogs_dev
```

## 🔧 Backend Setup

### 1. Clone Repository
```bash
# Clone the repository
git clone https://github.com/your-org/client-center.git
cd client-center
```

### 2. Restore .NET Packages
```bash
# Restore all project dependencies
dotnet restore

# Verify restore was successful
dotnet build
```

### 3. Configure Development Settings
Create or update the development configuration file:

```bash
# Navigate to the main API project
cd src/MyAdaptiveCloud
```

Create `appsettings.Development.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;User Id=myac_dev;Password=dev_password;Database=myadaptivecloud_dev;Connection Timeout=30;",
    "ACAgentConnection": "Server=localhost;User Id=myac_dev;Password=dev_password;Database=acagent_dev;Connection Timeout=30;",
    "LogsConnection": "Server=localhost;User Id=myac_dev;Password=dev_password;Database=myadaptivecloudlogs_dev;Connection Timeout=30;",
    "BillingConnection": "Server=localhost;User Id=myac_dev;Password=dev_password;Database=billing_dev;TrustServerCertificate=true;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "MYAC": "Debug"
    }
  },
  "AllowedHosts": "*"
}
```

### 4. Initialize Database Schema
The application uses Liquibase for database migrations. For local development, you can either:

**Option A: Use Docker (Recommended)**
```bash
# From project root
cd docker
docker-compose up myacdbliquibase
```

**Option B: Manual Schema Setup**
```bash
# If you have basic schema files, run them manually
mysql -u myac_dev -p myadaptivecloud_dev < src/MyAdaptiveCloud.Data/sql/myadaptivecloud/basic_schema.sql
```

### 5. Start the Backend API
```bash
# From src/MyAdaptiveCloud directory
dotnet run

# Or with hot reload for development
dotnet watch run
```

You should see output similar to:
```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7001
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://localhost:5001
```

## 🎨 Frontend Setup

### 1. Navigate to Angular Application
```bash
# From project root
cd src/MyAdaptiveCloud/ClientApp
```

### 2. Install Dependencies
```bash
# Install npm packages
npm install

# This may take a few minutes on first run
```

### 3. Create SSL Certificates (for HTTPS development)
```bash
# Create certs directory
mkdir certs

# Generate self-signed certificate (Windows/Linux)
openssl req -x509 -newkey rsa:4096 -keyout certs/localhost.key -out certs/localhost.crt -days 365 -nodes -subj "/CN=localhost"

# For macOS, you might need to install the certificate in Keychain
```

### 4. Start Angular Development Server
```bash
# Start with SSL (recommended)
npm start

# Or start without SSL
ng serve
```

The Angular app will be available at:
- **HTTPS**: https://localhost:4200 (recommended)
- **HTTP**: http://localhost:4200

## ✅ Verification

### 1. Check Backend API
Open your browser and navigate to:
- **API Health**: http://localhost:5001/api/health (if available)
- **Swagger/OpenAPI**: http://localhost:5001/swagger
- **API Base**: http://localhost:5001/api/

You should see API documentation or a JSON response.

### 2. Check Frontend Application
Navigate to: https://localhost:4200

You should see:
- MyAdaptiveCloud login page or dashboard
- No console errors in browser developer tools
- Network requests to the backend API

### 3. Verify Database Connections
Check the backend console output for:
```
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (Xms) [Parameters=[], CommandType='Text', CommandTimeout='30']
```

### 4. Test Full Stack Integration
1. **Login Flow**: Try logging in (if authentication is configured)
2. **API Calls**: Check browser Network tab for successful API calls
3. **Database**: Verify data is being read/written to the database

## 🚨 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check if port is in use
netstat -an | grep :5001

# Kill process using port (Windows)
netpid -ano | findstr :5001
taskkill /PID <process_id> /F

# Kill process using port (macOS/Linux)
lsof -ti:5001 | xargs kill -9
```

#### Database Connection Issues
```bash
# Test database connection
mysql -u myac_dev -p -h localhost -e "SELECT 1;"

# Check if MySQL is running
# Windows
sc query mysql

# macOS/Linux
brew services list | grep mysql
systemctl status mysql
```

#### Angular Build Errors
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18+
```

#### SSL Certificate Issues
```bash
# Trust the self-signed certificate in browser
# Or disable SSL for development:
ng serve --ssl=false
```

### Getting Help
- Check the detailed guides in the `onboarding/` folder
- Review application logs in the console
- Check browser developer tools for frontend issues
- Verify all prerequisites are correctly installed

## 🎯 Next Steps

Once everything is running:

1. **Explore the Codebase**: Review the detailed documentation in `/onboarding/`
2. **Set Up IDE**: Configure your preferred IDE with the project
3. **Run Tests**: Execute unit and integration tests
4. **Create a Feature Branch**: Start working on your first task

```bash
# Run backend tests
dotnet test

# Run frontend tests
cd src/MyAdaptiveCloud/ClientApp
npm run test-ci

# Create feature branch
git checkout -b feature/your-feature-name
```

**Congratulations! 🎉 Your MyAdaptiveCloud development environment is ready!**
