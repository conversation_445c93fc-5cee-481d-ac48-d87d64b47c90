﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Tenax.Model
{
    public class TenaxMSP
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

#nullable enable
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("domain")]
        public string? Domain { get; set; }
#nullable disable

        [JsonPropertyName("createdAt")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updatedAt")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("masterUsers")]
        public List<TenaxUser> Users { get; set; }
    }
}