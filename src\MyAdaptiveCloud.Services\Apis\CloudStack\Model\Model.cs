using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class CloudStackDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(
            ref Utf8JsonReader reader,
            Type typeToConvert,
            JsonSerializerOptions options) =>
                DateTime.ParseExact(reader.GetString(),
                    "yyyy-MM-ddTHH:mm:ss+0000", CultureInfo.InvariantCulture);
        // 2020-08-20'T'23:59:59+00:00
        /*
        JsonSerializerOptions options) =>
            DateTime.ParseExact(reader.GetString(),
                "yyyy-MM-dd\"'T'\"HH:mm:ss+00:00", CultureInfo.InvariantCulture);
        */
        // 2020-08-20'T'23:59:59+00:00

        public override void Write(
            Utf8JsonWriter writer,
            DateTime dateTimeValue,
            JsonSerializerOptions options) =>
                writer.WriteStringValue(dateTimeValue.ToString(CultureInfo.InvariantCulture));
    }
}
