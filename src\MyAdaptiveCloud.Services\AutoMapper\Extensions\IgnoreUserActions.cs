﻿using AutoMapper;
using MyAdaptiveCloud.Core.Common.UserActions;
using System.ComponentModel;

namespace MyAdaptiveCloud.Services.AutoMapper.Extensions
{
    public static class IgnoreExtensions
    {
        public static IMappingExpression<TSource, TDestination> IgnoreViewModelUserActions<TSource, TDestination>(
            this IMappingExpression<TSource, TDestination> expression)
        {
            var destinationType = typeof(TDestination);
            foreach (var property in destinationType.GetProperties())
            {
                PropertyDescriptor descriptor = TypeDescriptor.GetProperties(destinationType)[property.Name];
                var attribute = (UserActionAttribute)descriptor.Attributes[typeof(UserActionAttribute)];
                if (attribute != null)
                    expression.ForMember(property.Name, opt => opt.Ignore());
            }
            return expression;
        }
    }
}
