﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Logs;
using MyAdaptiveCloud.Data.MyAdaptiveCloudLogs;

namespace MyAdaptiveCloud.Services.AutoMapper.Logs
{
    public class LogDtoMapperProfile : Profile
    {
        public LogDtoMapperProfile()
        {
            CreateMap<ExtraInformation, ExtraInformationDTO>();
            CreateMap<ExtraInformationDTO, ExtraInformation>();
            CreateMap<LogEntryDTO, LogRecord>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationId, opt => opt.Ignore())
                .ForMember(dest => dest.HasEntityChanges, opt => opt.Ignore());
            CreateMap<LogEntryDetailDTO, LogRecordDetail>()
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.Level.ToString()))
                .ForMember(dest => dest.MasterLogRecordId, opt => opt.Ignore())
                .ForMember(dest => dest.MasterLogRecord, opt => opt.Ignore());
        }
    }
}