﻿using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.Ontap
{
    public class Utilities
    {
        public static async Task<string> ExtractErrorMessage(HttpResponseMessage response)
        {
            // Read the content of the response
            string content = await response.Content.ReadAsStringAsync();

            // Check if the response contains an error message
            if (response.IsSuccessStatusCode)
            {
                // If the status code indicates success, there shouldn't be an error message
                // You can return null or an appropriate response
                return null;
            }
            else
            {
                // If the response is not successful, attempt to extract the error message
                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(content))
                    {
                        // Check if the JSON contains an "error" node
                        if (doc.RootElement.TryGetProperty("error", out JsonElement errorNode))
                        {
                            // If there is an "error" node, try to get the "message" from it
                            if (errorNode.TryGetProperty("message", out JsonElement errorMessage))
                            {
                                // Return the error message
                                return errorMessage.GetString();
                            }
                        }
                    }
                }
                catch (JsonException)
                {
                    // If there is an issue with JSON parsing, handle it appropriately
                    // For example, you could log the error or return a default error message
                    return "Error in parsing JSON response.";
                }
            }

            // If there's no "error" node or "message" in the error node, return a default message or null
            return "Error occurred, but no specific message was found.";
        }
    }
}