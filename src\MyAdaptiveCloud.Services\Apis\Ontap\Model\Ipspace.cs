namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Ipspace : OntapObject
{
    public Ipspace() {}
    public Ipspace(string name): base(name) { }

    public Ipspace(string name, Guid uuid): base(name, uuid) { }

    public Ipspace(Guid uuid): base(uuid) { }
    
    public override string ToString()
    {
        return $"IP Space Name: {Name} Uuid: {Uuid}";
    }
}

public class UpdateIpspace {
    public UpdateIpspace() { }
    public string Name { get; set; }
    public UpdateIpspace(string name)  { 
        Name = name ;
    }
}