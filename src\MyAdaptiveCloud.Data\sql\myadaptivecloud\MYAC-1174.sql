
-- liquibase formatted sql

-- changeset lcerbino:0da791cd-be16-4213-88cc-cf7a4d45a2ab context:main
CREATE OR REPLACE TABLE `file_administration_folder` (
    `file_administration_folder_id` INT NOT NULL AUTO_INCREMENT,
    `organization_id` INT NOT NULL,
    `name` VARCHAR(1000) NOT NULL,
    `created_by` INT,
    `updated_by` INT,
    `parent_folder_Id` INT,
    `created_on` DATETIME NOT NULL,
    `updated_on` DATETIME,
    PRIMARY KEY (`file_administration_folder_id`),
    CONSTRAINT  fk_fileadministration_folder_parent FOREIGN KEY (`parent_folder_Id`) REFERENCES `file_administration_folder` (`file_administration_folder_id`)  ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT `fk_fileadministration_folder_created_by_id` FOREIGN KEY (`created_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CO<PERSON><PERSON><PERSON>INT `fk_fileadministration_folder_updated_by_id` <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`updated_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT `fk_fileadministration_folder_org_OrganizationId` FOREIGN KEY (`organization_id`) REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION   
);

-- changeset lcerbino:eef10cd9-b9aa-4187-b371-6fab04fa99f5 context:main
ALTER TABLE file_administration
  ADD COLUMN IF NOT EXISTS folder_id INT(11) NULL,
  ADD CONSTRAINT `fk_folder_administration_folderId` FOREIGN KEY IF NOT EXISTS (folder_id) REFERENCES `file_administration_folder` (file_administration_folder_id);

-- changeset lcerbino:12de016b-dc8a-4553-83f3-41f0b724b987 context:main
-- comment: create unique index on file_administration_folder table for name, organization_id, parent_folder_Id
CREATE UNIQUE INDEX IF NOT EXISTS `ux_name_organization_id_parent_folder_Id`
	ON `file_administration_folder` (`name`, `organization_id`, `parent_folder_Id`);