-- liquibase formatted sql

-- changeset ggoodrich:cf100f88-9b94-40de-bc6a-42dd48241981 context:main
CREATE TABLE IF NOT EXISTS `authentication_key` (
    `authentication_key_id` INT NOT NULL AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `name` VARCHAR(80) NOT NULL,
    `api_key` VARCHAR(36) NOT NULL,
    `api_secret` VARCHAR(52) NOT NULL,
    `require_signing` TINYINT(1) NOT NULL DEFAULT FALSE,
    `created_by` INT,
    `updated_by` INT,
    `created_on` DATETIME,
    `updated_on` DATETIME,
    PRIMARY KEY (`authentication_key_id`),
    CONSTRAINT `fk_authentication_key_created_by_user_id` FOREIGN KEY (`created_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	UNIQUE INDEX `ux_authentication_key_api_key` (`api_key`) USING BTREE
);

-- changeset lcerbino:911f93a1-14b7-49a0-9ed4-350af12e87f4 context:main
ALTER TABLE IF EXISTS authentication_key
    MODIFY COLUMN IF EXISTS api_key VARCHAR(36) NOT NULL UNIQUE;
	
-- changeset lcerbino:373e77fd-d760-4e64-8ca9-0f54c8bda792 context:main
-- Migrate the AgentAuthentication key pair into the new table
SELECT ConfigurationId INTO @configId FROM Configuration WHERE Category = 'AgentAuthentication';
SELECT COALESCE(Value, '') INTO @apiKey FROM ConfigurationValues WHERE ConfigurationId = @configId AND Name = 'ApiKey';
SELECT COALESCE(Value, '') INTO @apiSecret FROM ConfigurationValues WHERE ConfigurationId = @configId AND Name = 'ApiSecret';
SELECT UserId INTO @userId FROM `User` WHERE Email = '<EMAIL>';
INSERT IGNORE INTO authentication_key (user_id, name, api_key, api_secret, require_signing, created_by, created_on)
    VALUES (@userId, 'AgentAuthentication', @apiKey, @apiSecret, TRUE, @userId, UTC_TIMESTAMP());
