namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    public class Contact
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public bool InactiveFlag { get; set; }
        public bool DefaultFlag { get; set; }
        public CompanyRef Company { get; set; }
        public List<CommunicationItemRef> CommunicationItems { get; set; }
    }

    public class ContactRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class CommunicationItemRef
    {
        public int Id { get; set; }
        public CommunicationTypeRef Type { get; set; }
        public string Value { get; set; }
        public bool DefaultFlag { get; set; }
        public string Domain { get; set; }
        public string CommunicationType { get; set; }
    }

    public class CommunicationTypeRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}