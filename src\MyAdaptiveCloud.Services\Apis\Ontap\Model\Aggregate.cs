namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Aggregate: OntapObject, IComparable<Aggregate>
{
    public string State { get; set; }
    public AggregateSpace Space { get; set; }
    public Node Node { get; set; }
    
    public Aggregate() {}

    public Aggregate(Guid uuid)
    {
        Uuid = uuid;
    }

    public Aggregate(string name)
    {
        Name = name;
    }

    public int CompareTo(Aggregate other)
    {
        if (other == null)
        {
            return 1;
        }

        return FreePercentage().CompareTo(other.FreePercentage());
    }

    public override string ToString()
    {
        return $"Aggregate: {Name} {Uuid} {State} Size: {Space.BlockStorage.Size} Available: {Space.BlockStorage.Available}";
    }

    public float FreePercentage()
    {
        return (float)Space.BlockStorage.Available / Space.BlockStorage.Size;
    }
}

public class AggregateSpace
{
    public BlockStorage BlockStorage { get; set; }
}

public class BlockStorage
{
    public long Size { get; set; }
    public long Available { get; set; }
    public long Used { get; set; }
    public long PhysicalUsed { get; set; }
}