using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    // This currently doesn't work. Maybe it will in the future. Leaving it here for now,
    // as the work-around is kind of ugly. Has issues with nullable DateTimes
    public class ConnectWiseDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(
            ref Utf8JsonReader reader,
            System.Type typeToConvert,
            JsonSerializerOptions options) =>
                DateTime.Parse(reader.GetString(), CultureInfo.InvariantCulture);

        public override void Write(
            Utf8JsonWriter writer,
            DateTime dateTimeValue,
            JsonSerializerOptions options) =>
                writer.WriteStringValue(dateTimeValue.ToString("yyyy-MM-ddThh:mm:ss", CultureInfo.InvariantCulture));
    }
}
