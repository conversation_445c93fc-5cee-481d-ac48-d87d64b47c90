﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Role;

namespace MyAdaptiveCloud.Services.AutoMapper.Roles
{
    public class RoleMapperProfile : Profile
    {
        public RoleMapperProfile()
        {
            CreateMap<Data.MyAdaptiveCloud.Role, RoleDTO>()
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

            CreateMap<Data.MyAdaptiveCloud.AvailableOrgsByUser, AvailableOrgsByUserDTO>()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore());

            CreateMap<Data.MyAdaptiveCloud.AvailableOrganization, AvailableOrganizationDTO>()
                .ForMember(d => d.ParentOrganizationId, opt => opt.Ignore())
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore())
                .ForMember(d => d.ParentOrganization, opt => opt.Ignore());

            CreateMap<Data.MyAdaptiveCloud.UserByRole, UserByRoleDTO>()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore());
        }
    }
}