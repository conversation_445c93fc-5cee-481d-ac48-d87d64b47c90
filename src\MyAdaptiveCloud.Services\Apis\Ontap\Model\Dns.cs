namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Dns : OntapObject
{
    public List<string> Domains { get; set; }
    public List<string> Servers { get; set; }
    public Svm Svm { get; set; }
    
    public Dns() {}

    public Dns(List<string> domains, List<string> servers, Guid svm)
    {
        Svm = new Svm(svm);
        Domains = domains;
        Servers = servers;
    }

    public Dns(List<string> domains, List<string> servers)
    {
        Domains = domains;
        Servers = servers;
    }
}