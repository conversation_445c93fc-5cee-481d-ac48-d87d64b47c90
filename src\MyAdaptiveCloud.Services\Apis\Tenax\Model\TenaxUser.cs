﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Tenax.Model
{
    public class TenaxUser
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("firstName")]
        public string FirstName { get; set; }

        [<PERSON>son<PERSON>ropertyName("lastName")]
        public string LastName { get; set; }

        [Json<PERSON>ropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("status")]
        public int Status { get; set; }

        [JsonPropertyName("roles")]
        public string[] Roles { get; set; }

        [JsonPropertyName("createdAt")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updatedAt")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("lastLoginAt")]
        public DateTime LastLogin { get; set; }
    }
}