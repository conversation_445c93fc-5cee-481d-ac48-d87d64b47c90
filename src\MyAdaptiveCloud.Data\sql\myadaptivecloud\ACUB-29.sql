-- liquibase formatted sql

-- changeset ggoodrich:04f3f7ed-75e0-4d39-b611-9ab0d6b3cad3
ALTER TABLE IF EXISTS acusage_compute_rec ADD COLUMN IF NOT EXISTS isBillable TINYINT(1) DEFAULT 1;
UPDATE acusage_compute_rec SET isBillable = TRUE WHERE isBillable IS NULL;
ALTER TABLE IF EXISTS acusage_compute_rec MODIFY COLUMN IF EXISTS isBillable TINYINT(1) NOT NULL DEFAULT 1;

-- changeset ggoodrich:62204041-c9bf-491c-834e-a83a21222177
ALTER TABLE IF EXISTS acusage_compute_rec ADD COLUMN IF NOT EXISTS storage TEXT;
