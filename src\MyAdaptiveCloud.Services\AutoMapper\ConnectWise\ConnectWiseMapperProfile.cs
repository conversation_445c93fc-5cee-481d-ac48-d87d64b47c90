﻿using AutoMapper;
using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using MyAdaptiveCloud.Services.DTOs.Billing;
using MyAdaptiveCloud.Services.DTOs.Registration;

namespace MyAdaptiveCloud.Services.AutoMapper.ConnectWise
{
    public class ConnectWiseMapperProfile : Profile
    {
        public ConnectWiseMapperProfile()
        {
            CreateMap<Company, ConnectWiseCompanyDTO>()
                .ForMember(dest => dest.PrimaryContact, opt => opt.Ignore())
                .ForMember(dest => dest.IsMapped, opt => opt.Ignore())
                .ForMember(dest => dest.BillingCompany, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationNameAlreadyExists, opt => opt.Ignore());
            CreateMap<CompanyRef, ConnectWiseCompanyDTO>()
                .ForMember(dest => dest.PrimaryContact, opt => opt.Ignore())
                .ForMember(dest => dest.IsMapped, opt => opt.Ignore())
                .ForMember(dest => dest.BillingCompany, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationNameAlreadyExists, opt => opt.Ignore());
            CreateMap<Company, RegistrationCompanyDTO>()
                .ForMember(dest => dest.ContactStatus, opt => opt.Ignore());
            CreateMap<Contact, ConnectWiseCompanyDTO>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Identifier, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.IsMapped, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationNameAlreadyExists, opt => opt.Ignore())
                .ForMember(dest => dest.BillingCompany, opt => opt.Ignore())
                .ForMember(dest => dest.PrimaryContact, opt => opt.MapFrom((src, dest) =>
                {
                    var emailCommunicationItem = src.CommunicationItems?.FirstOrDefault(ci => ci.CommunicationType == "Email");
                    if (emailCommunicationItem != null)
                    {
                        return src.FirstName + " " + src.LastName + " <" + emailCommunicationItem.Value + ">";
                    }
                    return src.FirstName + " " + src.LastName;
                }));
        }
    }
}
