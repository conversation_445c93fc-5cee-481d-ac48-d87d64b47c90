﻿using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Apis.CloudStack;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Apis.Synchronize.Model
{
    [ApplicationType(Applications.CloudInfra)]
    public class CloudInfrastructureSynchronize : ISynchronize
    {
        private readonly MyAdaptiveCloudContext _context;
        private readonly ICloudStackApi _csApi;

        public CloudInfrastructureSynchronize(MyAdaptiveCloudContext context, ICloudStackApi csApi)
        {
            _context = context;
            _csApi = csApi;
        }

        public async Task UpdateOrganizationName(int organizationId, string name)
        {
            var organizationMappings = await _context.CloudInfraOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(x => x.OrganizationId == organizationId).ToListAsync();

            foreach (var organizationMapping in organizationMappings)
            {
                HttpResponseMessage result = await _csApi.UpdateAccountName(organizationMapping.AccountId, name);
                if (result.IsSuccessStatusCode)
                {
                    organizationMapping.AccountName = name;
                    await _context.SaveChangesAsync();
                }
            }
        }

        public async Task DisconnectApplication(int organizationId)
        {
            // TODO Should we remove the account and possibly domain?
            await Task.CompletedTask;
        }
    }
}