﻿using MyAdaptiveCloud.Data.MyAdaptiveCloud;

namespace MyAdaptiveCloud.Services.DTOs.AdaptiveCloud
{

    public class AcUsageTableModel
    {
        public string UsageType { get; set; }
        public int ProductMapId { get; set; }
        public string Label { get; set; }
        public string Units { get; set; }
        public double ActualUsage { get; set; }
        public double? Quantity { get; set; }
        public double? UnitPrice { get; set; }
        public decimal? Cost { get; set; }
        public DateTimeOffset Month { get; set; }
        public AcUsageTableFunctions Function { get; set; }
    }
}
