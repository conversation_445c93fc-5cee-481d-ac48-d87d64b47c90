﻿using System.Text.Json.Serialization;


namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class ImpersonatedUser
    {
        public string Username { get; set; }
        public string UserId { get; set; }
        public string DomainId { get; set; }
        public string Timeout { get; set; }
        public string Account { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Type { get; set; }
        public string Timezone { get; set; }
        public string TimezoneOffset { get; set; }
        public string Registered { get; set; }
        public string SessionKey { get; set; }
        public string Cookie { get; set; }

    }

    public class ImpersonateUserResponse
    {
        [JsonPropertyName("loginresponse")]
        public ImpersonatedUser ImpersonateUser { get; set; }
    }
}
