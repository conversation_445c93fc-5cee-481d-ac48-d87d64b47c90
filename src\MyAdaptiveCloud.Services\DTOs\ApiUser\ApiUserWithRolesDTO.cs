﻿using MyAdaptiveCloud.Core.Common;

namespace MyAdaptiveCloud.Services.DTOs.ApiUser
{
    public class ApiUserWithRolesDTO
    {
        public int PersonId { get; set; }

        public string Name { get; set; }

        public BasePerson CreatedBy { get; set; }

        public DateTimeOffset CreatedDate { get; set; }

        public int ApiUserId { get; set; }

        public List<string> Roles { get; set; }

        public bool CanEditUsers { get; set; }

        public bool CanDeleteUsers { get; set; }
    }
}
