﻿-- liquibase formatted sql

-- changeset voviedo:ba72c02d-8008-4bd2-9645-38c30761ec4b context:"main"
CREATE TABLE IF NOT EXISTS `Log_Records` (
    `Id` INT(11) NOT NULL AUTO_INCREMENT,
    `StartedOn` DATETIME NULL DEFAULT NULL,
    `TraceId` VARCHAR(100) NULL DEFAULT NULL,
    `OrganizationId` INT(11) NULL DEFAULT NULL,
    `UserId` INT(11) NULL DEFAULT NULL,
    `RemoteIP` VARCHAR(50) NULL DEFAULT NULL,
    `HasErrors` BIT(1) NULL DEFAULT NULL,
    `<PERSON><PERSON><PERSON><PERSON>Changes` BIT(1) NULL DEFAULT NULL,
    PRIMARY KEY (`Id`) );
	
-- changeset voviedo:fea22f81-ada8-4a19-8351-5bd7a754b649 context:"main"
CREATE TABLE IF NOT EXISTS `Log_Records_Detail` (
    `Id` INT(11) NOT NULL AUTO_INCREMENT,
    `MasterLogRecordId` INT(11) NOT NULL,
    `CreatedOn` DATETIME NULL DEFAULT NULL,
    `Level` INT(11) NULL DEFAULT NULL,
    `Message` TEXT NULL DEFAULT NULL,
    `ExtraInformation` LONGTEXT NULL DEFAULT NULL,
    PRIMARY KEY (`Id`) USING BTREE,
    INDEX `FK_log_records_detail_log_records` (`MasterLogRecordId`) USING BTREE,
    CONSTRAINT `FK_log_records_detail_log_records` FOREIGN KEY (`MasterLogRecordId`) REFERENCES `Log_Records` (`Id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT `ExtraInformation` CHECK (json_valid(`ExtraInformation`))
);
-- changeset voviedo:434f365e-25fe-47e0-a89c-9fca81e2b2c2 context:"main"
CREATE TABLE IF NOT EXISTS `Log_Records_Detail_Entities` (
    `Id` INT(11) NOT NULL AUTO_INCREMENT,
    `CreatedOn` DATETIME NULL DEFAULT NULL,
    `Level` INT(11) NULL DEFAULT NULL,
    `Message` TEXT NULL DEFAULT NULL,
    `EntityType` VARCHAR(100) NULL DEFAULT NULL,
    `PrimaryKey` VARCHAR(100) NULL DEFAULT NULL ,
    `ExtraInformation` LONGTEXT NULL DEFAULT NULL,
    `MasterLogRecordId` INT(11) NOT NULL DEFAULT 0,
    PRIMARY KEY (`Id`),
    INDEX `FK_log_records_detail_entities_log_records` (`MasterLogRecordId`) USING BTREE,
    CONSTRAINT `FK_log_records_detail_entities_log_records` FOREIGN KEY (`MasterLogRecordId`) REFERENCES `Log_Records` (`Id`) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- changeset voviedo:d17950bd-f42c-48fc-8548-f333307969b3 context:"main"
CREATE INDEX IF NOT EXISTS StartedOn ON Log_Records (StartedOn);
CREATE INDEX IF NOT EXISTS IX_TraceId ON Log_Records (TraceId);
CREATE INDEX IF NOT EXISTS IX_OrganizationId ON Log_Records (OrganizationId);
CREATE INDEX IF NOT EXISTS IX_UserId ON Log_Records (UserId);
CREATE INDEX IF NOT EXISTS IX_RemoteIP ON Log_Records (RemoteIP);
CREATE INDEX IF NOT EXISTS IX_HasErrors ON Log_Records (HasErrors);
CREATE INDEX IF NOT EXISTS IX_HasEntityChanges ON Log_Records (HasEntityChanges);
						   
-- changeset voviedo:ef35d49f-87b2-45ae-b889-e89da0d0144b context:"main"
CREATE INDEX IF NOT EXISTS IX_MasterLogRecordId ON Log_Records_Detail (MasterLogRecordId);
CREATE INDEX IF NOT EXISTS IX_CreatedOn ON Log_Records_Detail (CreatedOn);
						   
-- changeset voviedo:1729de73-4791-4b27-a7f0-915a29571eec context:"main"
CREATE INDEX IF NOT EXISTS IX_MasterLogRecordId ON Log_Records_Detail_Entities (MasterLogRecordId);
CREATE INDEX IF NOT EXISTS IX_CreatedOn ON Log_Records_Detail_Entities (CreatedOn);