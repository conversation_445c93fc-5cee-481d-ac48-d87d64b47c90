﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.DeviceControl;

namespace MyAdaptiveCloud.Services.AutoMapper.DeviceControl
{
    public class DeviceControlMapperProfile : Profile
    {
        public DeviceControlMapperProfile()
        {
            CreateMap<Data.MyAdaptiveCloud.DeviceControl.DeviceControlRole, DeviceControlRoleDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.DeviceControlRoleId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.DeviceControlRoleName));

            CreateMap<Data.MyAdaptiveCloud.DeviceControl.DeviceControl, DeviceControlDTO>()
               .ForMember(dest => dest.FullName, opt => opt.Ignore())
               .ForMember(dest => dest.DeviceControlRole, opt => opt.Ignore())
               .ForMember(dest => dest.DeviceControlId, opt => opt.MapFrom(src => src.DeviceControlId))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.UserEmail))
               .ForMember(dest => dest.DeviceControlRoleId, opt => opt.MapFrom(src => src.DeviceControlRoleId));
        }

    }
}
