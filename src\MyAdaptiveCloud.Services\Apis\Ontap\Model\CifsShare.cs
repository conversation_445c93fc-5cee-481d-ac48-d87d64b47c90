using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class CifsShare : OntapObject
{
    public string Path { get; set; }
    public List<CifsShareAcl> Acls { get; set; }
    public Svm Svm { get; set; }
    public Volume Volume { get; set; }

    public CifsShare()
    {
    }

    public CifsShare(Guid svmId, string svmName, string name, string path, List<CifsShareAcl> acls)
    {
        Name = name;
        Path = path;
        Svm = new Svm(svmId);
        Svm.Name = svmName;
        Acls = acls;
    }

    public override string ToString()
    {
        var volumeName = Volume?.Name ?? "";
        return $"Share: {Name} {Path} '{Svm}' {volumeName}";
    }
}

public class CifsShareAcl
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public AclPermission Permission { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public AclType Type { get; set; }

    public string UserOrGroup { get; set; }

    public CifsShareAcl()
    {
    }

    public CifsShareAcl(AclPermission permission, AclType type, string userOrGroup)
    {
        Permission = permission;
        Type = type;
        UserOrGroup = userOrGroup;
    }

    public CifsShareAcl(AclPermission permission)
    {
        Permission = permission;
    }

    public override string ToString()
    {
        return $"Share Acl: U:{UserOrGroup} T: {Type} P: {Permission}";
    }
}

public enum AclPermission
{
    [EnumMember(Value = "NONE")]
    None,
    NoAccess,
    Read,
    Change,
    FullControl
}

public enum AclType
{
    [EnumMember(Value = "NONE")]
    None,
    Windows,
    UnixUser,
    UnixGroup
}