﻿namespace MyAdaptiveCloud.Services.DTOs.CloudInfra
{
    public class AccountListModel
    {
        public int Id { get; set; }
        public Guid? CloudAccountId { get; set; }
        public string OrganizationName { get; set; }
        public string AccountName { get; set; }
        public string DomainName { get; set; }
        public bool IsMappedToCloudInfraAccount { get; set; }
        public string UsersCount { get; set; }
        public int OrganizationId { get; set; }
        public bool IsPartner { get; set; }
        public bool IsTopLevelOrganization { get; set; }

    }
}
