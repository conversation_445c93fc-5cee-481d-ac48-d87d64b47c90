using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class BroadcastDomainApi : TypeApi
{
    public BroadcastDomainApi(OntapApi client) : base(client)
    {
    }

    public async Task<List<BroadcastDomain>> GetBroadcastDomains()
    {
        return await Client.GetResources<BroadcastDomain>(UrlBuilder.ListUrl("/network/ethernet/broadcast-domains"));
    }

    public async Task<BroadcastDomain> GetBroadcastDomain(string name)
    {
        return await Client.SearchResource<BroadcastDomain>(UrlBuilder.ListUrl("/network/ethernet/broadcast-domains").AddQueryParam("name", name));
    }

    public async Task<BroadcastDomain> GetBroadcastDomain(Guid uuid)
    {
        return await Client.GetResource<BroadcastDomain>(UrlBuilder.ResourceUrl("/network/ethernet/broadcast-domains", uuid));
        // return await Client.SearchResource<BroadcastDomain>(UrlBuilder.ResourceUrl("/network/ethernet/broadcast-domains", uuid));
    }

    public async Task<BroadcastDomain> GetBroadcastDomainByIpspace(Guid uuid)
    {
        return await Client.SearchResource<BroadcastDomain>(UrlBuilder.ListUrl("/network/ethernet/broadcast-domains").AddQueryParam("ipspace.uuid", uuid));
    }

    public async Task<BroadcastDomain> CreateBroadcastDomain(string name, int mtu, Guid ipspaceUuid)
    {
        var body = new BroadcastDomain(name.ToLowerInvariant(), mtu, ipspaceUuid);
        return await Client.CreateResource(new UrlBuilder("/network/ethernet/broadcast-domains"), body);
    }

    public async Task<BroadcastDomain> CreateBroadcastDomain(string name, int mtu, Ipspace ipspace)
    {
        return await CreateBroadcastDomain(name, mtu, ipspace.Uuid);
    }

    public async Task UpdateBroadcastDomain(string name, Guid uuid)
    {
        var body = new UpdateBroadcastDomain(name);
        await Client.PatchResource(UrlBuilder.ResourceUrl("/network/ethernet/broadcast-domains", uuid), body);
    }

    public async Task DeleteBroadcastDomain(Guid uuid)
    {
        await Client.DeleteResource(UrlBuilder.ResourceUrl("/network/ethernet/broadcast-domains", uuid));
    }

    public async Task<Job> DeleteBroadcastDomain(BroadcastDomain broadcastDomain)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/network/ethernet/broadcast-domains", broadcastDomain.Uuid));
    }
}