-- liquibase formatted sql

-- changeset voviedo:4c89def6-3136-4eb6-8c56-f15617d755b3 context:main
ALTER TABLE device_alert MODIFY COLUMN started_on DATETIME NOT NULL;

-- changeset voviedo:fe87810e-81cb-485c-aaa4-dc87c20a937m context:main
CREATE OR REPLACE TABLE `device_alert_escalation_chain_notified` (
	`device_alert_escalation_chain_notified_id` INT(11) NOT NULL AUTO_INCREMENT,
	`device_alert_id` INT(11) NOT NULL,
	`escalation_chain_id` INT(11) NOT NULL,
	`escalation_chain_detail_id` INT(11) NOT NULL,
	`sent_at` DATETIME NOT NULL,
	iteration INT NOT NULL,
	PRIMARY KEY (`device_alert_escalation_chain_notified_id`) USING BTREE,
	INDEX `FK_device_alert_id_device_alert_device_alert_id` (`device_alert_id`) USING BTREE,
	INDEX `FK_escalation_chain_id_escalation_chain_escalation_chain_id` (`escalation_chain_id`) USING BTREE,
	INDEX `FK_esc_chain_detail_id_esc_chain_details_esc_chain_detail_id` (`escalation_chain_detail_id`) USING BTREE,
	CONSTRAINT `FK_device_alert_id_device_alert_device_alert_id` FOREIGN KEY (`device_alert_id`) REFERENCES `device_alert` (`device_alert_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `FK_esc_chain_detail_id_esc_chain_details_esc_chain_detail_id` FOREIGN KEY (`escalation_chain_detail_id`) REFERENCES `escalation_chain_details` (`EscalationChainDetailId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `FK_escalation_chain_id_escalation_chain_escalation_chain_id` FOREIGN KEY (`escalation_chain_id`) REFERENCES `escalation_chain` (`EscalationChainId`) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- changeset jbarrera:C0EB756F-501E-4879-8BFF-2259DA8A99B9 context:main
CALL AddConfigurationCategory('Alert Rule Evaluator Worker');
CALL SetConfigurationValue('Alert Rule Evaluator Worker','WorkerDelay','20000','input', 0);

-- changeset voviedo:4d4be7be-6339-4299-ada4-c1ae8a5a1af7 context:main
UPDATE device_alert_threshold_type SET description = 'GB Free' WHERE device_alert_threshold_type_id = 1;
UPDATE device_alert_threshold_type SET description = '%' WHERE device_alert_threshold_type_id = 2;

-- changeset voviedo:ac1adace-82f3-4d53-99c2-bbf4e474266b context:main
ALTER TABLE IF EXISTS device_alert MODIFY COLUMN IF EXISTS current_usage VARCHAR(500);