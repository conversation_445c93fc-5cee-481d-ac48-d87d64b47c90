namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    public class Agreement
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? BillStartDate { get; set; }
        public DateTime? NextInvoiceDate { get; set; }
        public bool? ProrateFlag { get; set; }
        public string AgreementStatus { get; set; }
        public AgreementTypeRef Type { get; set; }
        public CompanyRef Company { get; set; }
        public ContactRef Contact { get; set; }
        public BillingCycleRef BillingCycle { get; set; }
        public bool? NoEndingDateFlag { get; set; } ///< Set to True to have no ending date on this agreement, which should be the default
    }

    public class AgreementRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
    }

    public class AgreementTypeRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class CreateAgreement
    {
        public string Name { get; set; }
        public string StartDate { get; set; }
        public string BillStartDate { get; set; }
        public string AgreementStatus { get; set; }
        public AgreementTypeRef Type { get; set; }
        public CompanyRef Company { get; set; }
        public ContactRef Contact { get; set; }
        public BillingCycleRef BillingCycle { get; set; }
        public bool? NoEndingDateFlag { get; set; } ///< Set to True to have no ending date on this agreement, which should be the default
    }
    public class BillingCycleRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}