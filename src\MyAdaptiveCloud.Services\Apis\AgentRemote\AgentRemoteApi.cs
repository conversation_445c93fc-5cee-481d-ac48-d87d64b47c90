using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Apis.AgentRemote.Model;
using MyAdaptiveCloud.Services.Apis.AgentRemote.Requests;
using Microsoft.Extensions.Logging;

namespace MyAdaptiveCloud.Services.Apis.AgentRemote
{
    public class AgentRemoteApi : IAgentRemoteApi
    {
        private readonly HttpClient _client;
        private readonly ILogger<AgentRemoteApi> _logger;

        public AgentRemoteApi(HttpClient client, IConfigurationService configurationService, ILogger<AgentRemoteApi> logger)
        {
            _client = client;

            var acarConfig = configurationService.GetAgentRemoteConfiguration().GetAwaiter().GetResult();

            _client.BaseAddress = new Uri(acarConfig.BaseUrl);

            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _logger = logger;
        }

        public async Task<RemoteDesktopInitiateResponse> RemoteDesktopInitiate(string agentUuId, string agentPublicKeyBase64)
        {
            var response = await ExecutePostRequest("/remotedisplay/initiate",
                new RemoteDesktopInitiateRequest { AgentUuId = agentUuId, AgentPublicKeyBase64 = agentPublicKeyBase64 });
            return await GetDeserializedResponse<RemoteDesktopInitiateResponse>(response);
        }

        public async Task<RemoteDesktopStatusResponse> RemoteDesktopStatus(string agentUuId)
        {
            var response = await ExecuteGetRequest($"/remotedisplay/{agentUuId}/status", new Dictionary<string, string>());
            if (response.StatusCode == System.Net.HttpStatusCode.NoContent)
            {
                return null;
            }

            return await GetDeserializedResponse<RemoteDesktopStatusResponse>(response);
        }

        public async Task TerminateConnectionForAgent(string agentUuId)
        {
            var response = await ExecuteDeleteRequest($"/remotedisplay/{agentUuId}/terminate");
            if (!response.IsSuccessStatusCode)
            {
                // log error
                _logger.LogError("TerminateConnectionForAgent failed");
                _logger.LogError("Response: " + await response.Content.ReadAsStringAsync());
                _logger.LogError("StatusCode: " + response.StatusCode);
                _logger.LogError("ReasonPhrase: " + response.ReasonPhrase);
            }
        }

        // 04/25/22 BLG - added completionOption to control when HttpClient operation completes; after full response read (default) vs headers only
        private async Task<HttpResponseMessage> ExecuteGetRequest(string relativeUrlString, IDictionary<string, string> requestParams,
            HttpCompletionOption completionOption = HttpCompletionOption.ResponseContentRead)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = QueryHelpers.AddQueryString(_client.BaseAddress.AbsolutePath + relativeUrlString, requestParams);
            HttpResponseMessage response = await _client.GetAsync(relativeUriString, completionOption);
            return response;
        }

        private async Task<HttpResponseMessage> ExecutePostRequest<T>(string relativeUrlString, T body)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;

            var content = new StringContent(JsonSerializer.Serialize(body, new JsonSerializerOptions()
            {
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            }), Encoding.UTF8, "application/json");
            HttpResponseMessage response = await _client.PostAsync(relativeUriString, content);
            return response;
        }

        private async Task<HttpResponseMessage> ExecuteDeleteRequest(string relativeUrlString)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;

            HttpResponseMessage response = await _client.DeleteAsync(relativeUriString);
            return response;
        }

        private async Task<HttpResponseMessage> ExecutePatchRequest<T>(string relativeUrlString, T body)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;
            var content = new StringContent(JsonSerializer.Serialize(body, new JsonSerializerOptions()
            {
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            }), Encoding.UTF8, "application/json");
            HttpResponseMessage response = await _client.PatchAsync(relativeUriString, content);
            return response;
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            if (!response.IsSuccessStatusCode && response.StatusCode != System.Net.HttpStatusCode.NotFound)
            {
                // log error
                _logger.LogError("Response: " + await response.Content.ReadAsStringAsync());
                _logger.LogError("StatusCode: " + response.StatusCode);
                _logger.LogError("ReasonPhrase: " + response.ReasonPhrase);
                throw new Exception("An error has ocurred. Please contact your administrator ");
            }

            T res = new T();

            if (response.Content == null || response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return res;
            }

            _logger.LogInformation("Response: " + await response.Content.ReadAsStringAsync());

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };
            return await JsonSerializer.DeserializeAsync<T>(await response.Content.ReadAsStreamAsync(), options);
        }
    }
}