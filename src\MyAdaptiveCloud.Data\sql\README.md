# Using Liquibase to manage your database
NOTE: All CLI liquibase commands should be ran from a shell, and you should be within the same directory as the liquibase.properties file when running commands!

## Files
Note on files in `myadaptivecloud` directory:
* `apply_main_updates.sh` - Bash script that will apply all changesets in the `main` context to the database.  This script is intended to be used in the dev/test/prod environments.  On windows you can run it via git bash or VS Code Integrated Terminal.
* `*.sql` - SQL scripts that are intended for use in the dev/test/prod environments.  Should have no test/dev specific data, see [./myadaptivecloud/dev/README.md](./myadaptivecloud/dev/README.md) for test/dev specific data.
* `baseline*.json` - JSON files that are used to generate the baseline SQL scripts.
* `liquibase*properties` - Liquibase properties files that are used to configure the liquibase CLI tool.  More info below.

## Contexts
At this time only `main` changelog context should be used in this directory as these schema changes are intended for use in dev/lab/test and prod environments.

Any test/dev specific changes should be made in the `dev` directory [./myadaptivecloud/dev/README.md](./myadaptivecloud/dev/README.md).

## Test Data
Test/dev data should be added to the `dev` directory [./myadaptivecloud/dev/README.md](./myadaptivecloud/dev/README.md).

## Install Liquibase on your development machine:
[Download Liquibase](https://www.liquibase.org/download) or download from the [GitHub Releases page here](https://github.com/liquibase/liquibase/releases)
Follow the instructions on that page to install the version for your OS. Make sure you can invoke liquibase via your Os's CLI/command line via 'liquibase'. This may require adjusting your path and restarting any open shells.
You also need to have a DBO type user, which is a user that has all rights to do everything in the database engine. This is because some of the statements that we could be running could be things like GRANT's for other users.
After you've verified that this works, you can copy the file *liquibase.local.properties.template* to *liquibase.local.properties*, then edit this file to set the following settings in it for your myac user:

    liquibase.command.username: <yourdbuser>
    liquibase.command.password: <yourdbpass>

Once you have this, you can verify that liquibase can connect to your local database by issuing the following command:

    liquibase status

## Adjusting your local database to align with our baseline:
Before you can really leverage liquibase to manage your local db schema entirely, you need to get it into a **baseline** state. Essentially it is getting your local database to match our baseline from a well-known point in time. Once that is accomplished, you will be able to just run the *update* command whenever you need to ingest changes.
In order to see the differences between your current local database and this baseline, you need to run this command from within this folder, which will do a type of diff between our baseline and your local db and output sql statements that could be used to 'fix up' your local db to match the baseline:

    liquibase --changelog-file=mychangelog.mariadb.sql diff-changelog

Once you've run this command, you will have a new file named **mychangelog.mariadb.sql**. Open this file with your favorite editor, and look through the SQL statements that represent the difference between baseline and your local db. Here is an example of the types of things you could find in this file:

    -- changeset ggoodrich:1662675097896-4
    CREATE TABLE device_folder (FolderId INT AUTO_INCREMENT NOT NULL, ParentFolderId INT DEFAULT NULL NULL, OrganizationId INT NOT NULL, Name VARCHAR(50) NOT NULL, SortId SMALLINT UNSIGNED DEFAULT 0 NOT NULL, CONSTRAINT PK_DEVICE_FOLDER PRIMARY KEY (FolderId));

    -- changeset ggoodrich:1662675097896-7
    CREATE INDEX FK_device_folder_device_folder ON device_folder(ParentFolderId);

    -- changeset ggoodrich:1662675097896-8
    CREATE INDEX FK_organization ON device_folder(OrganizationId);

    -- changeset ggoodrich:1662675097896-9
    ALTER TABLE device_folder ADD CONSTRAINT FK_device_folder_device_folder FOREIGN KEY (ParentFolderId) REFERENCES device_folder (FolderId) ON UPDATE RESTRICT ON DELETE RESTRICT;

    -- changeset ggoodrich:1662675097896-10
    ALTER TABLE device_folder ADD CONSTRAINT FK_organization FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

    -- changeset ggoodrich:1662675097896-11
    ALTER TABLE `Organization` DROP COLUMN IsPartner;

    -- changeset ggoodrich:1662675097896-12
    ALTER TABLE `Organization` DROP COLUMN IsVerified;

So in this example, you can kind of work it backwards to understand that the baseline database has a table that the local db is lacking, named device\_folder, along with some indexes, foreign keys, and constraints on this table. Also, the baseline database DOES NOT have the columns IsPartner and IsVerified in the Organization table. This could be due to us recently adding those columns, and therefore the baseline doesn't have them yet.

So for this example, it should always be safe to apply any changes that are CREATEing new things, including ADDing columns, tables, constraints, etc. However, you will want to carefully evaluate any statements that could be destructive, such as the two DROP COLUMN statements, as you may have data in your local database that you don't want to lose.

If you are okay with all of the statements in the file being ran against your local database, then you can go about this in one of two ways:
1. Allow liquibase to apply them by running this command:
        liquibase --changelog-file=mychangelog.mariadb.sql update
2. Copy the commands into your favorite sql tool and run them, either as a group, or individually. If you run them individually, you should run them in the order that they are in this file, as sometimes the ordering can be important.

If there are statements in this file that you do not wish to run, usually to avoid local data loss, then you can try to verify if the change is something that will automatically get applied in one of our changelog files that we will be running next. For example, I happen to know that in this situation, the IsPartner and IsVerified columns are part of the updates in the initial changelog, which will carry our v1.4 changes. Due to this, I will be trying to ensure that these statements will work regardless of the status of your local database, so you can skip running these manually at this time.

## Running against your local - ongoing
Once you have your database aligned with the baseline, then it is quite simple to keep it up to date. Just run:
    liquibase update

## General Liquibase concepts
Liquibase automatically reads settings from the following two files:
* liquibase.properties - This file carries the bulk of the settings for how liquibase is set up to run for us. This file is committed to the GitHub Repo, and thus you should not change it unless necessary.
* liquibase.local.properties - This is where your local/personal settings go. This file should NOT be committed to the GitHub repository, as it will contain database passwords! You can copy the file *liquibase.local.properties.template* to this filename and edit it with your personal settings.

Liquibase has a main changelog that is specified in liquibase.properties. For us, that file will be **changelog.yaml**. It reads that file in, and then includes the files specified within, **IN ORDER**. Thus we always want newer changes to follow older changes, so that the changes will be able to be ran successfully. There are comments in changelog.yaml describing how to add your new includes in there, so please edit with caution!

I have included two baseline sql files. These files can be used as your 'reference database', and thus liquibase can 'diff' your local database against one of these. The two baselines are for v1.3 and v1.4. You will likely only need to use the v1.4 baseline, but I left the v1.3 in there just in case.

## Making database changes - schema and data
Create a new sql file prefixed with your story id, such as **MYAC-404.sql**. Feel free to add additional keywords at the end of your file if you wish, such as **MYAC-404AddingLiquibase.sql**. Use the snippet for Liquibase Changeset. Place your sql statement(s) directly under the changeset comment/header. Please ensure that your sql statements are idempotent if at all possible. Please test your sql to ensure that it is accurate.
Once you've got your new file with your sql changes in it, edit the main changelog file **changelog.yaml** and follow the comments inside this file to copy/paste a new include statement in the appropriate location with your new filename in it.

## Making database changes - stored procedures
The stored procedures are treated more like a c# source file. What this means is that all of them live in a single file called StoredProcs.sql. If you are creating a new stored procedure, please follow the pattern within this file, and add your new procedure(s) at the end of the file. Use the snippet for Liquibase Procedure to add the comment/header that is needed. Then add your stored procedure underneath this comment/header.
If you are altering an existing stored procedure, you may edit them in-place in this file. When liquibase detects that one of the procedures has been changed, it will automatically re-apply the new changes. As with the schema and data changes, make your stored procedure sql idempotent, which in this case means using *CREATE OR REPLACE PROCEDURE* at the beginning of the sql. This will ensure that the statement can be ran regardless of whether the procedure exists already or is brand new.
