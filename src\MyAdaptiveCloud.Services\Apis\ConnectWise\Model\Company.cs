using System.Text.Json;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    public class CustomField
    {
        public int Id { get; set; }

        public string Caption { get; set; }

        // This element in ConnectWise has a variable response type, thus the need to handle it special.
        // Namely it can be a string in quotes "myvalue", a number without quotes 1234, a null with no quotes, or a boolean with no quotes true or false
        // The Json Serializer won't handle that properly if I put string or bool here, so the code has to check the type
        // This element cannot serialize if it is not nullable
        public JsonElement Value { get; set; }
    }

    public class CompanyRef
    {
        public int Id { get; set; }

        /// <summary>This is the short name</summary>
        public string Identifier { get; set; }

        public string Name { get; set; }
    }

    public class Company : CompanyRef
    {
        public CompanyStatus Status { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public CountryRef Country { get; set; }
        public string PhoneNumber { get; set; }
        public CompanySiteRef Site { get; set; }
        public List<CompanyTypeRef> Types { get; set; }
        public ContactRef DefaultContact { get; set; }
        public bool LeadFlag { get; set; }
    }

    public class CompanyWithCustom : Company
    {
        public List<CustomField> CustomFields { get; set; }
    }

    public class CompanyStatus
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class StateRef
    {
        public int Id { get; set; }
        public string Identifier { get; set; }
        public string Name { get; set; }
    }

    public class CountryRef
    {
        public int Id { get; set; }
        public string Identifier { get; set; }
        public string Name { get; set; }
    }

    public class CompanySiteRef
    {
        public int? Id { get; set; }
        public string Name { get; set; }
    }

    public class CompanySite : CompanySiteRef
    {
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string City { get; set; }
        public string Zip { get; set; }

        [JsonPropertyName("stateReference")]
        public StateRef State { get; set; }

        public CountryRef Country { get; set; }
        public string PhoneNumber { get; set; }

        [JsonPropertyName("primaryAddressFlag")]
        public bool IsPrimaryAddress { get; set; }

        [JsonPropertyName("defaultShippingFlag")]
        public bool IsDefaultShippingAddress { get; set; }

        [JsonPropertyName("defaultBillingFlag")]
        public bool IsDefaultBillingAddress { get; set; }

        [JsonPropertyName("billSeparateFlag")]
        public bool UseSeparateBillingAsShippingAddresses { get; set; }
    }

    public class CompanyTypeRef
    {
        public int? Id { get; set; }
        public string Name { get; set; }
    }

    public class CompanyType : CompanyTypeRef
    {
    }
}