using System.Security.Cryptography;
using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.AutoMapper.Extensions;
using MyAdaptiveCloud.Services.DTOs.AuthenticationKeys;
using MyAdaptiveCloud.Services.Requests.AuthenticationKey;

namespace MyAdaptiveCloud.Services.AutoMapper.AuthenticationKeys
{
    public class AuthenticationKeyMapperProfile : Profile
    {
        public AuthenticationKeyMapperProfile()
        {
            CreateMap<AuthenticationKey, AuthenticationKeyDTO>()
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .IgnoreViewModelUserActions();
            CreateMap<AuthenticationKey, CreateAuthenticationKeyDTO>()
                // It should only be mapped when creating the key for the first time
                .ForMember(dest => dest.ApiSecret, opt => opt.Ignore());
            CreateMap<CreateAuthenticationKeyRequest, AuthenticationKey>()
                .ForMember(dest => dest.ApiKey, opt => opt.MapFrom(src => Guid.NewGuid().ToString()))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.ApiSecret, opt => opt.MapFrom(src => GenerateRandomString(52)))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Person, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.PersonId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore());
        }

        private static string GenerateRandomString(int length)
        {
            char[] charSet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".ToCharArray();
            char[] rStr = new char[length];
            var rBytes = RandomNumberGenerator.GetBytes(length);
            for (var idx = 0; idx < length; idx++)
            {
                rStr[idx] = charSet[rBytes[idx] % charSet.Length];
            }
            return new string(rStr);
        }

    }
}