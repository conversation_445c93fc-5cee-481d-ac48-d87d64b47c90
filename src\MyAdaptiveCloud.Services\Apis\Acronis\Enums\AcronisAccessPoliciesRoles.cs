﻿using System.ComponentModel;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Enums
{
    public enum AcronisAccessPoliciesRoles
    {
        [Description("root_admin")]
        RootAdmin,
        [Description("partner_admin")]
        PartnerAdmin,
        [Description("hci_admin")]
        HCIAdmin,
        [Description("company_admin")]
        CompanyAdmin,
        [Description("unit_admin")]
        UnitAdmin,
        [Description("readonly_admin")]
        ReadonlyAdmin,
        [Description("protection_admin")]
        ProtectionAdmin,
        [Description("protection_ro_admin")]
        ProtectionRoAdmin,
        [Description("restore_operator")]
        RestoreOperator,
        [Description("backup_user")]
        BackupUser,
        [Description("sync_share_admin")]
        SyncShareAdmin,
        [Description("sync_share_guest")]
        SyncShareGuest,
        [Description("sync_share_user")]
        SyncShareUser,
        [Description("pds_operator")]
        PdsOperator,
        [Description("pds_support")]
        PdsSupport,
        [Description("notary_admin")]
        NotaryAdmin,
        [Description("notary_user")]
        NotaryUser,
        [Description("omnivoice_admin")]
        OmnivoiceAdmin,
        [Description("omnivoice_user")]
        OmnivoiceUser,
        [Description("greathorn_admin")]
        GreathornAdmin,
        [Description("greathorn_user")]
        GreathornUser,
        [Description("greathorn_analyst")]
        GreathornAnalyst,
        [Description("greathorn_client_manager")]
        GreathornClientManager
    }
}
