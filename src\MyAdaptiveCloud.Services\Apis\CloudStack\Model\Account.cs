using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class Account
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public Guid DomainId { get; set; }

        [JsonPropertyName("domain")]
        public string DomainName { get; set; }

        [JsonPropertyName("vmtotal")]
        public int VmTotal { get; set; }

        [JsonPropertyName("vmlimit")]
        public string VmLimit { get; set; }

        [JsonPropertyName("iptotal")]
        public int IpTotal { get; set; }

        [JsonPropertyName("iplimit")]
        public string IpLimit { get; set; }

        [JsonPropertyName("cputotal")]
        public int CpuTotal { get; set; }

        [JsonPropertyName("cpulimit")]
        public string CpuLimit { get; set; }

        [Json<PERSON>ropertyName("memorytotal")]
        public int MemoryTotal { get; set; }

        [JsonPropertyName("memorylimit")]
        public string MemoryLimit { get; set; }

        [JsonPropertyName("primarystoragetotal")]
        public int PrimaryStorageTotal { get; set; }

        [JsonPropertyName("primarystoragelimit")]
        public string PrimaryStorageLimit { get; set; }

        [JsonPropertyName("secondarystoragetotal")]
        public double SecondaryStorageTotal { get; set; }

        [JsonPropertyName("secondarystoragelimit")]
        public string SecondaryStorageLimit { get; set; }

        public AccountType AccountType { get; set; }
    }

    public class ListAccountsResponse : CloudStackListResponse<AccountResponse>
    {
        [JsonPropertyName("account")]
        public override List<AccountResponse> Items { get; set; } = new List<AccountResponse>();
    }

    public enum AccountType
    {
        User,
        RootAdmin,
        DomainAdmin,
    }


    public class AccountResponse : Account
    {
        public List<User> user { get; set; }
    }

    public class CreateAccountResponse
    {
        public int count { get; set; }
        public AccountResponse account { get; set; }
    }

    public class CreateAccountRequest
    {
        public string Username { get; set; }

        ///< Name of the user, should match email
        public string Email { get; set; }

        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string Password { get; set; }
        public string Account { get; set; }

        ///< Name of the account
        public AccountType Accounttype { get; set; }

        public Guid? Domainid { get; set; }
        public string Networkdomain { get; set; }
        public string Timezone { get; set; }
    }
}