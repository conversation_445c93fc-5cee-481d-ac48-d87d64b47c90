using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Apis.PowerDNS.Model;
using MyAdaptiveCloud.Services.Services;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.PowerDNS
{
    public interface IPowerDNSApi
    {
        Task AddCNameRecord(string zoneName, string name, string canonicalName);
        Task AddRecord(string zoneName, ResourceRecordSet rrset);
        Task DeleteCNameRecord(string zoneName, string name);
        Task<Zone> GetZone(string zoneName);
    }

    public class PowerDNSApi : IPowerDNSApi
    {
        private readonly HttpClient _client;
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly IConfigurationService _configurationService;
        private readonly bool apiDisabled;
        public const string configurationCategoryKeyName = "PowerDNS";
        public const string baseUrlKeyName = "PDNSBaseUrl";
        public const string apiKeyKeyName = "PDNSApiKey";
        public const string defaultTtlKeyName = "PDNSDefaultTTL";
        public const string apiPrefix = "/api/v1";
        public const string serverId = "localhost";

        ///< This is always localhost for authoritative answers from the given server
        public PowerDNSApi(HttpClient client, MyAdaptiveCloudContext context, IConfigurationService configurationService)
        {
            _client = client;
            _dbContext = context;
            _configurationService = configurationService;

            var pdnsconfig = _configurationService.GetPowerDNSConfig().GetAwaiter().GetResult();
            var baseUrl = pdnsconfig.PDNSBaseUrl;
            var apiKey = pdnsconfig.PDNSApiKey;
            apiDisabled = string.IsNullOrWhiteSpace(apiKey);

            _client.BaseAddress = !string.IsNullOrEmpty(baseUrl) ? new Uri($"{baseUrl}{apiPrefix}/servers/{serverId}") : null;
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Add("X-Api-Key", apiKey);
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        private string FixupEntry(string entry)
        {
            if (entry[entry.Length - 1] != '.')
            {
                return entry + ".";
            }

            return entry;
        }

        public async Task<Zone> GetZone(string zoneName)
        {
            string relativeUriString = $"/zones/{FixupEntry(zoneName)}";
            return await ExecuteGetRequest<Zone>(relativeUriString);
        }

        public async Task AddCNameRecord(string zoneName, string name, string canonicalName)
        {
            string relativeUriString = $"/zones/{FixupEntry(zoneName)}";
            ResourceRecordSet rrset = new ResourceRecordSet
            {
                name = FixupEntry(name),
                type = "CNAME",
                records = new List<Record> { new Record { content = FixupEntry(canonicalName) } },
                changetype = "REPLACE",
                comments = new List<Comment>(),
            };
            await AddRecord(zoneName, rrset);
        }

        public async Task AddRecord(string zoneName, ResourceRecordSet rrset)
        {
            string relativeUriString = $"/zones/{FixupEntry(zoneName)}";
            var config = await _configurationService.GetPowerDNSConfig();
            var defaultTtl = Convert.ToInt32(config.PDNSDefaultTTL ?? "86400");
            rrset.ttl = rrset.ttl > 0 ? rrset.ttl : defaultTtl;
            var zone = new ZoneLite
            {
                rrsets = new List<ResourceRecordSet> { rrset },
            };
            var result = await ExecutePatchRequest(relativeUriString, zone);
            Console.WriteLine(result);
        }

        public async Task DeleteCNameRecord(string zoneName, string name)
        {
            string relativeUriString = $"/zones/{FixupEntry(zoneName)}";
            ResourceRecordSet rrset = new ResourceRecordSet
            {
                name = FixupEntry(name),
                type = "CNAME",
                records = new List<Record>(),
                changetype = "DELETE",
                comments = new List<Comment>(),
            };
            var zone = new ZoneLite
            {
                rrsets = new List<ResourceRecordSet> { rrset },
            };
            var result = await ExecutePatchRequest(relativeUriString, zone);
            Console.WriteLine(result);
        }

        private async Task<TResponse> ExecuteGetRequest<TResponse>(string relativeUrlString, IDictionary<string, string> requestParams = null) where TResponse : new()
        {
            if (apiDisabled)
            {
                return new TResponse();
            }

            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            //string relativeUriString = QueryHelpers.AddQueryString(_client.BaseAddress.AbsolutePath + relativeUrlString, requestParams ?? new Dictionary<string, string>());
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;
            HttpResponseMessage response = await _client.GetAsync(relativeUriString);
            return await GetDeserializedResponse<TResponse>(response);
        }

        private async Task<HttpResponseMessage> ExecutePatchRequest<TBody>(string relativeUrlString, TBody body)
        {
            if (apiDisabled)
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.OK);
            }

            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;
            var content = new StringContent(JsonSerializer.Serialize(body, new JsonSerializerOptions()
            {
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            }), Encoding.UTF8, "application/json");
            HttpResponseMessage response = await _client.PatchAsync(relativeUriString, content);
            return response;
        }

        private async Task<TResponse> GetDeserializedResponse<TResponse>(HttpResponseMessage response) where TResponse : new()
        {
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception("An error has ocurred. Please contact your administrator " + await response.Content.ReadAsStringAsync());
            }

            TResponse res = new TResponse();

            if (response.Content == null)
            {
                return res;
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };
            return await JsonSerializer.DeserializeAsync<TResponse>(await response.Content.ReadAsStreamAsync(), options);
        }
    }
}