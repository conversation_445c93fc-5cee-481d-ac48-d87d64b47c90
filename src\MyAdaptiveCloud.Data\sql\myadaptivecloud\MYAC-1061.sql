-- liquibase formatted sql

-- changeset lcerbino:9b416290-06ff-4d38-b414-c20c5eb2ed27 context:main
CALL `SetConfigurationValue`('Feature Flags', 'FeatureFlagAPIUser', 'true', 'input', 0);

-- changeset lcerbino:5d679a34-e04c-47e5-839b-2dd2dfc075f3 context:main
ALTER TABLE IF EXISTS User ADD COLUMN IF NOT EXISTS is_api_user BIT DEFAULT 0;

-- changeset lcerbino:ef7b75f5-02fd-4ba0-b664-8f174ecf3287 context:"main"
CREATE INDEX IF NOT EXISTS is_api_user ON User (is_api_user);