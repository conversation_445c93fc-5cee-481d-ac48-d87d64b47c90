﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisUser
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        [JsonPropertyName("version")]
        public int Version { get; set; }
        [JsonPropertyName("tenant_id")]
        public string TenantId { get; set; }
        [JsonPropertyName("login")]
        public string Login { get; set; }
        [JsonPropertyName("contact")]
        public AcronisContact Contact { get; set; }
        [JsonPropertyName("activated")]
        public bool Activated { get; set; }
        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; }
        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }
        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }
        [JsonPropertyName("deleted_at")]
        public string DeletedAt { get; set; }
        [JsonPropertyName("language")]
        public string Language { get; set; }
        [JsonPropertyName("idp_id")]
        public string IdpId { get; set; }
        [JsonPropertyName("external_id")]
        public string ExternalId { get; set; }
        [JsonPropertyName("personal_tenant_id")]
        public string PersonalTenantId { get; set; }
        [JsonPropertyName("business_types")]
        public List<string> BusinessTypes { get; set; }
        [JsonPropertyName("notifications")]
        public List<string> Notifications { get; set; }
        [JsonPropertyName("mfa_status")]
        public string MfaStatus { get; set; }

    }
}
