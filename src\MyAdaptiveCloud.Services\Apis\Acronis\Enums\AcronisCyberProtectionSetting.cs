﻿using System.ComponentModel;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Enums
{
    public enum AcronisCyberProtectionOfferingItem
    {
        [Description("pw_base_workstations")]
        PerWorkloadWorkstations,
        [Description("pw_base_servers")]
        PerWorkloadServers,
        [Description("pw_base_vms")]
        PerWorkloadVms,
        [Description("pw_base_mobiles")]
        PerWorkloadMobiles,
        [Description("pw_base_nas")]
        PerWorkloadNas,
        [Description("pw_base_websites")]
        PerWorkloadWebsites,
        [Description("pw_base_web_hosting_servers")]
        PerWorkloadWebHostingServers,
        [Description("pw_base_hosted_exchange")]
        PerWorkloadHostedExchange,
        [Description("pw_pack_adv_backup_m365_seats")]
        PerWorkloadAdvancedBackup365Seats,
        [Description("pw_pack_adv_backup_workstations")]
        PerWorkloadAdvancedBackupWorkstations,
        [Description("pw_pack_adv_backup_servers")]
        PerWorkloadAdvancedBackupServers,
        [Description("pw_pack_adv_backup_vms")]
        PerWorkloadAdvancedBackupVms,
        [Description("pw_pack_adv_backup_web_hosting_servers")]
        PerWorkloadAdvancedBackupWebHostingServers,
        [Description("pw_pack_adv_management")]
        PerWorkloadAdvancedManagement,
        [Description("pw_pack_adv_security")]
        PerWorkloadAdvancedSecurity,
        [Description("pw_pack_adv_email_security")]
        PerWorkloadAdvancedEmailSecurity,
        [Description("local_storage")]
        LocalStorage,
        [Description("pw_base_storage")]
        PerWorkloadStorage,
        [Description("pw_base_m365_mailboxes")]
        PerWorkloadMicrosoftOffice365Mailboxes,
        [Description("pw_base_m365_seats")]
        PerWorkloadMicrosoftOffice365Seats,
        [Description("pw_base_m365_onedrive")]
        PerWorkloadMicrosoftOffice365OneDrive,
        [Description("pw_base_m365_sharepoint_sites")]
        PerWorkloadMicrosoftOffice365SharepointSites,
        [Description("pw_base_m365_teams")]
        PerWorkloadMicrosoftOffice365Teams,
    }
}
