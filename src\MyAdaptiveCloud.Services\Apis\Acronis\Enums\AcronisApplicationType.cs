﻿using System.ComponentModel;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Enums
{
    public enum AcronisApplicationType
    {
        [Description("Cyber Protection")]
        CyberProtection,
        [Description("Physical Data Shipping")]
        PhysicalDataShipping,
        [Description("File Sync & Share")]
        FileSyncAndShare,
        [Description("Notary")]
        Notary,
        [Description("Cyber Infrastructure")]
        CyberInfrastructure,
        [Description("Omnivoice")]
        Omnivoice,
        [Description("GreatHorn Cloud Email Security")]
        GreatHornCloudEmailSecurity,
        [Description("Advanced Automation")]
        AdvancedAutomation,
        [Description("IPN")]
        IPN,
        [Description("Acronis Support")]
        AcronisSupport,
        [Description("Acronis Partner Service")]
        AcronisPartnerService,
        [Description("Management Portal")]
        ManagementPortal,
    }
}
