using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class Domain
    {
        [Key]
        public Guid Id { get; set; }

        public string Name { get; set; }
        public int? Level { get; set; }
        public Guid? ParentDomainId { get; set; }
        public string ParentDomainName { get; set; }
        public string NetworkDomain { get; set; }
        public string State { get; set; }
        public int VmTotal { get; set; }
        public string VmLimit { get; set; }
        public int IpTotal { get; set; }
        public string IpLimit { get; set; }
        public int CpuTotal { get; set; }
        public string CpuLimit { get; set; }
        public int MemoryTotal { get; set; }
        public string MemoryLimit { get; set; }
        public int PrimaryStorageTotal { get; set; }
        public string PrimaryStorageLimit { get; set; }
        public double SecondaryStorageTotal { get; set; }
        public string SecondaryStorageLimit { get; set; }
    }

    public class ListDomainsResponse : CloudStackListResponse<Domain>
    {
        [JsonPropertyName("domain")]
        public override List<Domain> Items { get; set; } = new List<Domain>();
    }

    public class CreateDomainResponse
    {
        public Domain domain { get; set; }
    }

    public class ListDomainChildrenResponse : CloudStackListResponse<Domain>
    {
        [JsonPropertyName("domain")]
        public override List<Domain> Items { get; set; } = new List<Domain>();
    }
}