﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    public class Configuration
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("type")]
        public Type Type { get; set; }

        [JsonPropertyName("company")]
        public CompanyRef Company { get; set; }
    }

    public class Type
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }
    }
}
