using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Caching.Memory;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Services.Apis.KeyCloak.Model;
using KeyCloakUser = MyAdaptiveCloud.Services.Apis.KeyCloak.Model.User;
using MyAdaptiveCloud.Services.Requests.Profile;
using KeyCloakAuthenticatorProviders = MyAdaptiveCloud.Services.Apis.KeyCloak.Model.Credential;
using Microsoft.Extensions.Logging;
using ResetPasswordRequest = MyAdaptiveCloud.Services.Apis.KeyCloak.Model.ResetPasswordRequest;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Apis.KeyCloak
{
    public class KeyCloakApi : IKeyCloakApi
    {
        private readonly HttpClient _client;
        private readonly IMemoryCache _cache;
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly ILogger<IKeyCloakApi> _logger;
        private readonly IConfigurationService _configurationService;
        private readonly string _authorityUrl;
        private readonly string _clientId;
        private readonly string _clientSecret;

        private string ApiUrl
        {
            get
            {
                return _cache.GetOrCreate(CacheEntryKeys.KeyCloakApiURLKey, entry =>
                {
                    // KeyCloak authority urls look like https://<host>/auth/realms/<realm-name>
                    // KeyCloak Api urls, however, need  https://<host>/auth/admin/realms/<realm-name>
                    // So we have the authority url, we can split it apart and inject the admin part
                    entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddDays(1);
                    var realmsIdx = _authorityUrl.IndexOf("/realms");
                    if (realmsIdx < 0)
                    {
                        return null;
                    }

                    return _authorityUrl.Substring(0, realmsIdx) + "/admin" + _authorityUrl.Substring(realmsIdx);
                });
            }
        }

        public KeyCloakApi(HttpClient client, MyAdaptiveCloudContext dbContext, IMemoryCache cache, ILogger<IKeyCloakApi> logger, IConfigurationService configurationService)
        {
            _client = client;
            _cache = cache;
            _dbContext = dbContext;
            _configurationService = configurationService;
            var configuration = _configurationService.GetKeyCloakServiceConfiguration().GetAwaiter().GetResult();
            _authorityUrl = configuration.AuthorityKey;
            _clientId = configuration.ApiKey;
            _clientSecret = configuration.ApiSecretKey;
            _client.BaseAddress = !string.IsNullOrEmpty(_authorityUrl) ? new Uri(_authorityUrl) : null;
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _logger = logger;
        }

        private async Task<Token> GetAccessToken()
        {
            var token = await _cache.GetOrCreate(CacheEntryKeys.KeyCloakApiTokenKey, async (entry) =>
            {
                using (var request = new HttpRequestMessage(HttpMethod.Post, _authorityUrl + "/protocol/openid-connect/token"))
                {
                    Dictionary<string, string> requestParams = new Dictionary<string, string>
                    {
                        { "scope", "openid" },
                        { "grant_type", "client_credentials" },
                    };
                    request.Headers.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(_clientId + ":" + _clientSecret)));
                    request.Content = new FormUrlEncodedContent(requestParams);
                    var tokenResponse = await GetDeserializedResponse<Token>(await _client.SendAsync(request));
                    entry.AbsoluteExpiration = DateTimeOffset.UtcNow.AddSeconds(tokenResponse.expires_in * .9);
                    return tokenResponse;
                }
            });

            return token;
        }

        public async Task<KeyCloakUser> GetUserByUsername(string email)
        {
            if (string.IsNullOrEmpty(email))
                return null;

            List<KeyCloakUser> users = null;
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "username", email }, ///< We always want the username to be their email address
                { "exact", "true" }, ///< only retrieve an exact match
            };
            string relativeUriString = QueryHelpers.AddQueryString(ApiUrl + "/users", requestParams);

            // In case no user is found, the SendAsync method can't parse the json and it would throw, so catch it and return null
            try
            {
                using (var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString))
                {
                    users = await SendAsync<List<KeyCloakUser>>(request);
                }

                if (users != null && users.Count > 0)
                    return users[0];
                else
                    return null;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, $"{nameof(GetUserByUsername)} KeyCloak API error");
                return null;
            }
        }

        public async Task<KeyCloakUser> GetUser(string keycloakUserId)
        {
            KeyCloakUser user = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(ApiUrl + $"/users/{keycloakUserId}")))
            {
                user = await SendAsync<KeyCloakUser>(request);
            }

            return user;
        }

        public async Task<List<KeyCloakUser>> GetUsersList()
        {
            List<KeyCloakUser> usersList = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(ApiUrl + $"/users")))
            {
                usersList = await SendAsync<List<KeyCloakUser>>(request);
            }

            return usersList;
        }

        public async Task<List<KeyCloakAuthenticatorProviders>> GetOtpAuthenticatorProviders(string keyCloakUserId)
        {
            List<KeyCloakAuthenticatorProviders> otpAuthenticatorList = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(ApiUrl + $"/users/{keyCloakUserId}/credentials")))
            {
                otpAuthenticatorList = await SendAsync<List<KeyCloakAuthenticatorProviders>>(request);
            }

            return otpAuthenticatorList.Where(a => a.Type == "otp").ToList();
        }

        public async Task DeleteAuthenticatorProvider(string userId, string credentialId)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Delete, new Uri(ApiUrl + $"/users/{userId}/credentials/{credentialId}")))
            {
                await SendAsync(request);
            }
        }

        public async Task<HttpResponseMessage> UpdateUser(KeyCloakUser keycloakUser)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Put, new Uri(ApiUrl + $"/users/{keycloakUser.Id}")))
            {
                request.Content = new StringContent(JsonSerializer.Serialize(keycloakUser, new JsonSerializerOptions()
                {
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                }), Encoding.UTF8, "application/json");
                return await SendAsync(request);
            }
        }


        public async Task SetUserAttributes(string keycloakUserId, Dictionary<string, List<string>> attributes)
        {
            // Expected values
            /*
            attributes = new Dictionary<string, List<string>> {
                    { "myac.organization.id", new List<string> { "332" } },
                    { "myac.organization.name", new List<string> { "Joe's Pizza Shop" } }
                };
            */
            bool dirtyFlag = false;
            var user = await GetUser(keycloakUserId);
            if (user.UserAttributes == null)
            {
                user.UserAttributes = attributes;
                dirtyFlag = true;
            }
            else
            {
                // MYAC-259 MindMatrix requirement for KeyCloak Org attributes:
                // flag to skip setting Org attributes on KeyCloak user if they are already set
                bool skipOrgAttributes = user.UserAttributes.ContainsKey(KeyCloakConstants.KeyCloakAttributeOrgId)
                                         && user.UserAttributes[KeyCloakConstants.KeyCloakAttributeOrgId].Count > 0
                                         && !string.IsNullOrEmpty(user.UserAttributes[KeyCloakConstants.KeyCloakAttributeOrgId][0])
                                         || user.UserAttributes.ContainsKey(KeyCloakConstants.KeyCloakAttributeOrgName)
                                         && user.UserAttributes[KeyCloakConstants.KeyCloakAttributeOrgName].Count > 0
                                         && !string.IsNullOrEmpty(user.UserAttributes[KeyCloakConstants.KeyCloakAttributeOrgName][0]);

                foreach (var keyValuePair in attributes)
                {
                    // if current attr key is an Org attribute and skipOrgAttributes is true, continue to next attr
                    if ((keyValuePair.Key == KeyCloakConstants.KeyCloakAttributeOrgId ||
                         keyValuePair.Key == KeyCloakConstants.KeyCloakAttributeOrgName) && skipOrgAttributes)
                        continue;

                    // Re-assign each dict key with our new values, but don't touch any keys that aren't mentioned in the incoming dict.
                    user.UserAttributes[keyValuePair.Key] = keyValuePair.Value;
                    dirtyFlag = true;
                }
            }

            if (dirtyFlag)
                await UpdateUser(user);
        }

        public async Task UpdateAuthenticatorProvider(string keyCloakUserId, KeyCloakAuthenticatorProviders authenticatorProvider)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Put, new Uri(ApiUrl + $"/users/{keyCloakUserId}/credentials/{authenticatorProvider.Id}/userLabel")))
            {
                request.Content = new StringContent(authenticatorProvider.UserLabel, Encoding.UTF8, "text/plain");
                var result = await SendAsync(request);
            }
        }

        public async Task<bool> ResetPassword(string keyCloakUserId, string newPassword)
        {
            ResetPasswordRequest newPasswordRequest = new ResetPasswordRequest()
            {
                Id = keyCloakUserId,
                Value = newPassword
            };

            using (var request = new HttpRequestMessage(HttpMethod.Put, new Uri(ApiUrl + $"/users/{keyCloakUserId}/reset-password")))
            {
                request.Content = new StringContent(JsonSerializer.Serialize(newPasswordRequest, new JsonSerializerOptions()
                {
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                }), Encoding.UTF8, "application/json");

                var result = await SendAsync(request);

                return result.IsSuccessStatusCode;
            }
        }

        public async Task<PasswordPolicyRequest> GetPasswordPolicy()
        {
            PasswordPolicyRequest result = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(ApiUrl + $"/")))
            {
                result = await SendAsync<PasswordPolicyRequest>(request);
            }

            return result;
        }

        public async Task DeleteUser(string keyCloakUserId)
        {
            using var request = new HttpRequestMessage(HttpMethod.Delete, new Uri(ApiUrl + $"/users/{keyCloakUserId}"));
            await SendAsync(request);
        }

        private async Task<T> SendAsync<T>(HttpRequestMessage request) where T : new()
        {
            var token = await GetAccessToken();
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.access_token);

            return await GetDeserializedResponse<T>(await _client.SendAsync(request));
        }

        private async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request)
        {
            var token = await GetAccessToken();
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.access_token);
            var result = await _client.SendAsync(request);
            // Log error in case it happens but don't rethrow.
            // Potentially there are calls where we want the flow to continue. 
            try
            {
                result.EnsureSuccessStatusCode();
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "KeyCloak API error");
            }

            return result;
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            T res = new T();

            if (response.Content == null)
            {
                return res;
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };
            return await JsonSerializer.DeserializeAsync<T>(await response.Content.ReadAsStreamAsync(), options);
        }
    }
}