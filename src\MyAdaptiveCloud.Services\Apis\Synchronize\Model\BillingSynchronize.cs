﻿using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.Billing;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Apis.Synchronize.Model
{
    [ApplicationType(Applications.BillingDB)]
    public class BillingSynchronize : ISynchronize
    {
        private readonly BillingContext _dbContext;

        public BillingSynchronize(BillingContext context)
        {
            _dbContext = context;
        }

        public async Task UpdateOrganizationName(int organizationId, string name)
        {
            await Task.CompletedTask;
        }

        public async Task DisconnectApplication(int organizationId)
        {
            // TODO Should we remove the entry from the billing db?
            await Task.CompletedTask;
        }
    }
}
