using MyAdaptiveCloud.Core.Common;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class User : BasePerson
    {
        const string RootDomainName = "ROOT";
        const string RootAdminRole = "Admin";
        const string DomainAdminRole = "DomainAdmin";
        public Guid Id { get; set; }
        public string Username { get; set; }
        public string State { get; set; }
        public string Account { get; set; }
        public Guid AccountId { get; set; }
        public int AccountType { get; set; }
        public Guid RoleId { get; set; }
        public string RoleType { get; set; }
        public string RoleName { get; set; }
        public Guid DomainId { get; set; }
        public string Domain { get; set; }

        [JsonPropertyName("usersource")]
        public string Source { get; set; }

        public bool IsRootAdmin
        {
            get { return RoleType == RootAdminRole; }
        }

        public bool IsDomainAdmin
        {
            get
            {
                // We could choose to do this several ways. We could check the RoleType of DomainAdmin, RoleName of 'Domain Admin'
                // or we could use the RoleId and query out for a specific value attached to that. Either way, we may want to make this
                // a ConfigurationValues settable thing at some point.
                return RoleType == DomainAdminRole;
            }
        }

        public bool IsPartnerUser
        {
            get
            {
                // If the user is a domain admin, and the domain is not the root domain, then this
                // must be a user of a partner. I'm pretty sure this value will always be ROOT on any installation
                return IsDomainAdmin && Domain != RootDomainName;
            }
        }

        public bool IsSuborgUser
        {
            get
            {
                // If the user is not a domain admin, and the domain is not the root domain, then this
                // must be a user of a partner's Suborganization, aka a partner's customer.
                return !IsDomainAdmin && Domain != RootDomainName;
            }
        }
    }
}