using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.KeyCloak.Model
{
    /// <summary>
    ///    KeyCloak User Representation
    ///    https://www.keycloak.org/docs-api/11.0/rest-api/index.html#_userrepresentation
    /// </summary>
    public class User
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("username")]
        public string UserName { get; set; }

        [JsonPropertyName("emailVerified")]
        public bool EmailVerified { get; set; }

        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; }

        [JsonPropertyName("firstName")]
        public string FirstName { get; set; }

        [JsonPropertyName("lastName")]
        public string LastName { get; set; }

        [Json<PERSON>ropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("requiredActions")]
        public string[] RequiredActions { get; set; }

        [JsonPropertyName("attributes")]
        public Dictionary<string, List<string>> UserAttributes { get; set; }
    }
}