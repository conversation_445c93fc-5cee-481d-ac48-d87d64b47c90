namespace MyAdaptiveCloud.Services.Apis.CloudStack.Enums
{
    public enum CloudInfrastructureResourceTypeEnum
    {
        Instances = 0,
        PublicIp = 1,
        Volume = 2,
        Snapshot = 3,
        Template = 4,
        Project = 5,
        Network = 6,
        Vpc = 7,
        Cpu = 8,
        Memory = 9,
        PrimaryStorage = 10,
        SecondaryStorage = 11
    }

    public delegate string MaxLimitForDisplayDelegate(int maxLimit);

    public class CloudInfrastructureResourceType
    {
        private CloudInfrastructureResourceType(CloudInfrastructureResourceTypeEnum type, string name,
            string displayName, string description, bool isAdaptiveCloudControled, string unit,
            MaxLimitForDisplayDelegate maxLimitForDisplayDelegate)
        {
            Type = type;
            Name = name;
            DisplayName = displayName;
            Description = description;
            IsAdaptiveCloudControled = isAdaptiveCloudControled;
            Unit = unit;
            MaxLimitForDisplayDelegate = maxLimitForDisplayDelegate;
        }

        public static CloudInfrastructureResourceType FromEnumString(string type)
        {
            int typeInt = 0;
            int.TryParse(type, out typeInt);

            return FromEnum((CloudInfrastructureResourceTypeEnum)typeInt);
        }

        public static CloudInfrastructureResourceType FromEnum(CloudInfrastructureResourceTypeEnum type)
        {
            switch (type)
            {
                case CloudInfrastructureResourceTypeEnum.Instances:
                    return Instances;
                case CloudInfrastructureResourceTypeEnum.PublicIp:
                    return PublicIp;
                case CloudInfrastructureResourceTypeEnum.Volume:
                    return Volume;
                case CloudInfrastructureResourceTypeEnum.Snapshot:
                    return Snapshot;
                case CloudInfrastructureResourceTypeEnum.Template:
                    return Template;
                case CloudInfrastructureResourceTypeEnum.Project:
                    return Project;
                case CloudInfrastructureResourceTypeEnum.Network:
                    return Network;
                case CloudInfrastructureResourceTypeEnum.Vpc:
                    return Vpc;
                case CloudInfrastructureResourceTypeEnum.Cpu:
                    return Cpu;
                case CloudInfrastructureResourceTypeEnum.Memory:
                    return Memory;
                case CloudInfrastructureResourceTypeEnum.PrimaryStorage:
                    return PrimaryStorage;
                case CloudInfrastructureResourceTypeEnum.SecondaryStorage:
                    return SecondaryStorage;
                default:
                    return null;
            }
        }

        public string GetMaxLimitForDisplayWithUnit(int maxLimit)
        {
            if (maxLimit == -1)
            {
                return "Unlimited";
            }
            else
            {
                if (MaxLimitForDisplayDelegate == null)
                {
                    return maxLimit.ToString();
                }
                else
                {
                    return MaxLimitForDisplayDelegate(maxLimit);
                }
            }
        }

        public CloudInfrastructureResourceTypeEnum Type { get; private set; }
        public string Name { get; private set; }
        public string DisplayName { get; private set; }
        public string Description { get; private set; }
        public bool IsAdaptiveCloudControled { get; private set; }
        public string Unit { get; private set; }

        /**
         * The delegate to get the max limit for display.
         * This delegate is used to calculate the max limit for display taking into consideration units.
         */
        private MaxLimitForDisplayDelegate MaxLimitForDisplayDelegate { get; }

        public static CloudInfrastructureResourceType Instances = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Instances, "user_vm", "Number of VMs",
            "Maximum number of Virtual Machine instances a user can create.", true, "", null);

        public static CloudInfrastructureResourceType PublicIp = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.PublicIp, "public_ip", "Number of Public IPs",
            "Maximum number of public IP addresses an account can own.", true, "", null);

        public static CloudInfrastructureResourceType Volume = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Volume, "volume", "Max Number of Disk Volumes",
            "Maximum number of disk volumes an account can own.", false, "", null);

        public static CloudInfrastructureResourceType Snapshot = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Snapshot, "snapshot", "Max Number of Snapshots",
            "Maximum number of snapshots an account can own.", false, "", null);

        public static CloudInfrastructureResourceType Template = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Template, "template", "Max Number of Templates",
            "Maximum number of templates an account can register/create.", false, "", null);

        public static CloudInfrastructureResourceType Project = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Project, "project", "Max Number of Projects",
            "Maximum number of projects an account can own.", false, "", null);

        public static CloudInfrastructureResourceType Network = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Network, "network", "Max Number of Networks",
            "Maximum number of networks an account can own.", false, "", null);

        public static CloudInfrastructureResourceType Vpc = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Vpc, "vpc", "Number of VPCs",
            "Maximum number of VPC an account can own.", false, "", null);

        public static CloudInfrastructureResourceType Cpu = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Cpu, "cpu", "Number of vCPUs",
            "Maximum number of CPU an account can allocate for his resources.", true, "", null);

        public static CloudInfrastructureResourceType Memory = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.Memory, "memory", "Amount of Memory",
            "Maximum amount of RAM an account can allocate for his resources.", true, "MB",
            (maxLimit) => { return maxLimit / 1000 + " GB"; });

        public static CloudInfrastructureResourceType PrimaryStorage = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.PrimaryStorage, "primary_storage", "Amount of Primary Storage",
            "Total primary storage space (in GiB) a user can use.", true, "GB",
            (maxLimit) => { return maxLimit + " GB"; });

        public static CloudInfrastructureResourceType SecondaryStorage = new CloudInfrastructureResourceType(
            CloudInfrastructureResourceTypeEnum.SecondaryStorage, "secondary_storage", "Amount of Secondary Storage",
            "Total secondary storage space (in GiB) a user can use.", true, "GB",
            (maxLimit) => { return maxLimit + " GB"; });
    }
}