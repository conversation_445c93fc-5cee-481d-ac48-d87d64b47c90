using MyAdaptiveCloud.Services.Apis.Ontap.Model;
using MyAdaptiveCloud.Services.Apis.Ontap.Model.CliRequests;
using System.Dynamic;
using System.Net.Http.Json;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class CifsApi : TypeApi
{
    public CifsApi(OntapApi client) : base(client)
    {
    }

    public async Task SetupWithLocalUsers(string serverName, string name, string workgroupName)
    {
        var createBody = new VserverCifsCreate(serverName.ToLowerInvariant(), name.ToLowerInvariant(), workgroupName.ToLowerInvariant());
        var response = await Client.ExecutePostRequest(new UrlBuilder("/private/cli/vserver/cifs"), createBody);
        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException("Unable to enable CIFS on SVM");
        }
    }

    public async Task UpdateCifsNameForWorkgroup(string serverName, string name)
    {
        var body = new VserverCifsUpdate(name.ToLowerInvariant());

        var url = new UrlBuilder("/private/cli/vserver/cifs");
        url.AddQueryParam("vserver", serverName.ToLowerInvariant());
        var response = await Client.ExecutePatchRequest(url, body);

        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException($"Unable to update CIFS on SVM.");
        }
    }

    public async Task UpdateCifsNameForActiveDirectory(Guid svmId, string name, string adDomain, string adUser, string adPassword)
    {
        var body = new CifsServiceUpdate(name.ToLowerInvariant(), adDomain.ToLowerInvariant(), adUser, adPassword);
        var url = UrlBuilder.ListUrl("/protocols/cifs/services/%svm%", "svm", svmId.ToString()).AddQueryParam("force", "true");
        var response = await Client.ExecutePatchRequest(url, body);

        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to update Netbios. {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task UpdateWorkgroup(string serverName, string workgroup)
    {
        var body = new VserverCifsUpdateWorkgroup(workgroup.ToLowerInvariant());

        var url = new UrlBuilder("/private/cli/vserver/cifs");
        url.AddQueryParam("vserver", serverName.ToLowerInvariant());
        var response = await Client.ExecutePatchRequest(url, body);

        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException($"Unable to update Workgroup on SVM.");
        }
    }

    public async Task<ListResponse<CifsService>> SetupWithAd(Guid svmId, string netbiosName, string fqdn, string user, string password)
    {
        var body = new CifsService(svmId, netbiosName.ToLowerInvariant(), fqdn, user, password);
        return await Client.CreateResourceAsync(
            new UrlBuilder("/protocols/cifs/services").AddQueryParam("force", "true"), body);
    }

    public async Task<CifsService> GetConfig(Guid svmId)
    {
        return await Client.GetResource<CifsService>(new UrlBuilder("/protocols/cifs/services/%uuid%")
            .AddPathParam("uuid", svmId));
    }

    public async Task EditActiveDirectoryInstance(string serverName, string domain, string user, string password)
    {
        var body = new VserverCifsUpdateActiveDirectory(domain.ToLowerInvariant(), user, password);

        var url = new UrlBuilder("/private/cli/vserver/cifs");
        url.AddQueryParam("vserver", serverName.ToLowerInvariant());
        try
        {
            await Client.PatchResource(url, body);
        }
        catch (Exception ex)
        {
            throw new OntapApiException($"Unable to update CIFS Server. {ex.Message}");
        }
    }

    public async Task<List<CifsUser>> GetLocalUsers(Guid svmId)
    {
        return await Client.GetResources<CifsUser>(UrlBuilder.ListUrl("/protocols/cifs/local-users").AddQueryParam("svm.uuid", svmId));
    }

    public async Task<CifsUser> CreateLocalUser(Guid svmId, string svmName, string username, string password, string fullName,
        string description, bool disabled)
    {
        var body = new CifsUser(username, password, fullName, description, disabled, svmId, svmName.ToLowerInvariant());
        return await Client.CreateResource(new UrlBuilder("/protocols/cifs/local-users"), body);
    }

    public async Task<CifsUser> GetLocalUserByName(Guid svmId, string username)
    {
        var users = await Client.GetResources<CifsUser>(UrlBuilder.ListUrl("/protocols/cifs/local-users").AddQueryParam("svm.uuid", svmId).AddQueryParam("name", username));
        if (users.Count == 1)
        {
            return users.First();
        }

        return null;
    }

    public async Task<CifsUser> GetLocalUserBySid(Guid svmId, string userSid)
    {
        var users = await Client.GetResources<CifsUser>(UrlBuilder.ListUrl("/protocols/cifs/local-users").AddQueryParam("svm.uuid", svmId).AddQueryParam("sid", userSid));
        if (users.Count == 1)
        {
            return users.First();
        }

        return null;
    }

    public async Task SetLocalUserPassword(Guid svmId, string userSid, string password)
    {
        var body = new CifsUser(password);
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-users/%svm%/%userSid%", "svm", svmId.ToString(), "userSid", userSid);
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to modify user. {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task UpdateLocalUser(Guid svmId, string userSid, string username, string fullName,
        string description, bool disabled)
    {
        var body = new CifsUser(username, fullName, description, disabled);
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-users/%svm%/%userSid%", "svm", svmId.ToString(), "userSid", userSid);
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to modify user. {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task UpdateLocalAdministratorUserPassword(Guid svmId, string userSid, string password)
    {
        dynamic body = new ExpandoObject();
        body.password = password;
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-users/%svm%/%userSid%", "svm", svmId.ToString(), "userSid", userSid);
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException($"Unable to modify CIFS local Administrator user '{userSid}' on SVM '{svmId}'");
        }
    }

    public async Task DeleteLocalUser(Guid svmId, string userSid)
    {
        var url = new UrlBuilder("/protocols/cifs/local-users/%svm%/%userSid%")
            .AddPathParam("svm", svmId)
            .AddPathParam("userSid", userSid);
        var response = await Client.ExecuteDeleteRequest(url);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to delete user. {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task<List<CifsGroup>> GetLocalGroups(Guid svmId)
    {
        return await Client.GetResources<CifsGroup>(UrlBuilder.ListUrl("/protocols/cifs/local-groups").AddQueryParam("svm.uuid", svmId));
    }

    public async Task<CifsGroup> GetLocalGroupByName(Guid svmId, string groupName)
    {
        return await Client.SearchResource<CifsGroup>(UrlBuilder.ListUrl("/protocols/cifs/local-groups")
            .AddQueryParam("svm.uuid", svmId)
            .AddQueryParam("name", groupName));
    }

    public async Task<CifsGroup> GetLocalGroupBySid(Guid svmId, string groupSid)
    {
        return await Client.SearchResource<CifsGroup>(UrlBuilder.ListUrl("/protocols/cifs/local-groups")
            .AddQueryParam("svm.uuid", svmId)
            .AddQueryParam("sid", groupSid));
    }

    public async Task<CifsGroup> CreateLocalGroup(Guid svmId, string svmName, string name, string description)
    {
        var body = new CifsGroup(name.ToLowerInvariant(), description.ToLowerInvariant(), svmId, svmName.ToLowerInvariant());
        return await Client.CreateResource(new UrlBuilder("/protocols/cifs/local-groups"), body);
    }

    public async Task DeleteCifsLocalGroup(Guid svmId, string sid)
    {
        var url = new UrlBuilder("/protocols/cifs/local-groups/%svm%/%sid%")
            .AddPathParam("svm", svmId)
            .AddPathParam("sid", sid);
        var response = await Client.ExecuteDeleteRequest(url);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"{content.Error?.Message ?? "Unable to delete the group."}");
        }
    }

    public async Task UpdateLocalGroup(Guid svmId, string sid, string name, string description)
    {
        var body = new CifsGroup(name, description);
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-groups/%svm%/%sid%", "svm", svmId.ToString(), "sid", sid);
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException($"Unable to update the group. {content.Error?.Message ?? String.Empty}");
        }
    }

    public async Task<List<CifsGroupMember>> GetLocalGroupMembers(Guid svmId, string groupSid)
    {
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-groups/%svm%/%groupSid%/members")
            .AddPathParam("svm", svmId)
            .AddPathParam("groupSid", groupSid);
        return await Client.GetResources<CifsGroupMember>(url);
    }

    public async Task AddMembersToLocalGroup(Guid svmId, string groupSid, List<string> users)
    {
        var body = new CifsGroupMemberAdd(users);
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-groups/%svm%/%groupSid%/members", "svm", svmId.ToString(), "groupSid", groupSid);
        var response = await Client.ExecutePostRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException(content.Error?.Message ?? "Unable to add users to group.");
        }
    }

    public async Task RemoveMembersFromLocalGroup(Guid svmId, string groupSid, List<string> users)
    {
        var url = UrlBuilder.ListUrl("/protocols/cifs/local-groups/%svm%/%groupSid%/members", "svm", svmId.ToString(), "groupSid", groupSid);
        var body = new CifsGroupMemberAdd(users);
        var response = await Client.ExecuteDeleteRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadFromJsonAsync<OntapApiErrorResponse>();
            throw new OntapApiException(content.Error?.Message ?? "Unable to remove users from group.");
        }
    }

    public async Task DeleteCifsWithLocalUsers(string vserver)
    {
        var success = (await Client.ExecuteDeleteRequest(new UrlBuilder("/private/cli/vserver/cifs").AddQueryParam("vserver", vserver))).IsSuccessStatusCode;
        if (!success)
        {
            throw new OntapApiException("Unable to delete CIFS config");
        }
    }

    public async Task<Job> DeleteCifs(Guid svmId)
    {
        return await Client.DeleteResourceAsync(new UrlBuilder("/protocols/cifs/services/%uuid%")
            .AddPathParam("uuid", svmId));
    }

    public async Task<Job> DeleteCifsWithAd(Guid svmId, string user, string password)
    {
        var body = new CifsService(user, password);
        return await Client.DeleteResourceAsync(new UrlBuilder("/protocols/cifs/services/%uuid%")
            .AddPathParam("uuid", svmId), body);
    }

    public async Task<List<CifsShare>> GetShares(Guid svmId)
    {
        return await Client.GetResources<CifsShare>(UrlBuilder.ListUrl("/protocols/cifs/shares").AddQueryParam("svm.uuid", svmId));
    }

    public async Task<List<CifsShare>> GetShares(IEnumerable<Guid> svmIds)
    {
        return await Client.GetResources<CifsShare>(UrlBuilder.ListUrl("/protocols/cifs/shares").AddQueryParam("svm.uuid", string.Join(",", svmIds)));
    }

    public async Task<CifsShare> CreateShare(Guid svmId, string svmName, string name, string path, List<CifsShareAcl> acls)
    {
        var body = new CifsShare(svmId, svmName.ToLowerInvariant(), name.ToLowerInvariant(), path.ToLowerInvariant(), acls);
        return await Client.CreateResource(new UrlBuilder("/protocols/cifs/shares"), body);
    }

    public async Task DeleteShare(Guid svmId, string share)
    {
        var url = new UrlBuilder("/protocols/cifs/shares/%svm%/%share%")
            .AddPathParam("svm", svmId)
            .AddPathParam("share", share);
        var response = await Client.ExecuteDeleteRequest(url);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            throw new OntapApiException($"Unable to delete share: {content}");
        }
    }

    public async Task<List<CifsShareAcl>> GetShareAcls(Guid svmId, string share)
    {
        var url = UrlBuilder.ListUrl("/protocols/cifs/shares/%svm%/%share%/acls")
            .AddPathParam("svm", svmId)
            .AddPathParam("share", share);
        return await Client.GetResources<CifsShareAcl>(url);
    }

    public async Task<CifsShareAcl> CreateShareAcl(Guid svmId, string share, string userOrGroup, AclType type, AclPermission permission)
    {
        var body = new CifsShareAcl(permission, type, userOrGroup);
        var url = new UrlBuilder("/protocols/cifs/shares/%svm%/%share%/acls")
            .AddPathParam("svm", svmId)
            .AddPathParam("share", share);
        return await Client.CreateResource(url, body);
    }

    public async Task<CifsShareAcl> GetShareAcl(Guid svmId, string share, string userOrGroup, AclType type)
    {
        var url = UrlBuilder.ListUrl("/protocols/cifs/shares/%svm%/%share%/acls/%userOrGroup%/%type%", "svm", svmId.ToString(), "share", share, "userOrGroup", userOrGroup, "type",
            type.ToString());
        return await Client.SearchResource<CifsShareAcl>(url);
    }

    public async Task RemoveShareAcl(Guid svmId, string share, string userOrGroup, AclType type)
    {
        var url = UrlBuilder.ListUrl("/protocols/cifs/shares/%svm%/%share%/acls/%userOrGroup%/%type%", "svm", svmId.ToString(), "share", share, "userOrGroup", userOrGroup, "type",
            type.ToString());
        await Client.ExecuteDeleteRequest(url);
    }

    public async Task ModifyShareAcl(Guid svmId, string share, string userOrGroup, AclType type, AclPermission permission)
    {
        var body = new CifsShareAcl(permission);
        // Console.WriteLine($"Body: {body}");
        var url = UrlBuilder.ListUrl("/protocols/cifs/shares/%svm%/%share%/acls/%userOrGroup%/%type%", "svm", svmId.ToString(), "share", share, "userOrGroup", userOrGroup, "type",
            type.ToString());
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            throw new OntapApiException($"Unable to update share acl {content}");
        }
    }
}