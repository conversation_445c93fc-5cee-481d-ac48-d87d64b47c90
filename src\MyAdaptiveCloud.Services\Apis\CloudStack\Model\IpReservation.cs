using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model;

public class IpReservation
{
    public Guid Id { get; set; }
    public string StartIp { get; set; }
    public string EndIp { get; set; }
    public string NetworkId { get; set; }

    public override string ToString()
    {
        return $"IP Reservation: {Id} Network: {NetworkId} Range: {StartIp}-{EndIp}";
    }
}

public class ListIpReservation : CloudStackListResponse<IpReservation>
{
    [JsonPropertyName("ipReservation")]
    public override List<IpReservation> Items { get; set; } = new List<IpReservation>();
}

public class GenerateIpReservation
{
    public IpReservation IpReservation { get; set; }
}