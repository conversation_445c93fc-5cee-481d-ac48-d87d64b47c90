﻿namespace MyAdaptiveCloud.Services.DTOs.AlertRules
{
    public class AlertThresholdLevelDTO
    {
        public int DeviceAlertThresholdLevelId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public AlertThresholdLevelEnumDTO Value { get; set; }
    }

    public enum AlertThresholdLevelEnumDTO
    {
        Warning = 1,
        Error = 2,
        Critical = 3
    }
}
