####     _     _             _ _
##      | |   (_)           (_) |
##      | |    _  __ _ _   _ _| |__   __ _ ___  ___
##      | |   | |/ _` | | | | | '_ \ / _` / __|/ _ \
##      | |___| | (_| | |_| | | |_) | (_| \__ \  __/
##      \_____/_|\__, |\__,_|_|_.__/ \__,_|___/\___|
##                  | |
##                  |_|
##
##      The liquibase.properties file stores properties which do not change often,
##      such as database connection information. Properties stored here save time
##      and reduce risk of mistyped command line arguments.
##      Learn more: https://docs.liquibase.com/concepts/connections/creating-config-properties.html
####
####
##   Note about relative and absolute paths:
##      The liquibase.properties file requires paths for some properties.
##      The classpath is the path/to/resources (ex. src/main/resources).
##      The changeLogFile path is relative to the classpath.
##      The url H2 example below is relative to 'pwd' resource.
####
# Enter the path for your changelog file.
changeLogFile=changelog.yaml

#### Enter the Target database 'url' information  ####
liquibase.command.url=*************************************************

# Enter the username for your Target database.
liquibase.command.username: myac

# Set your db password in the liquibase.local.properties file as the option below
# Enter the password for your Target database.
#liquibase.command.password: yourdbpass

#### Enter the Source Database 'referenceUrl' information ####
## The source database is the baseline or reference against which your target database is compared for diff/diffchangelog commands.



# Enter the username for your source database
#liquibase.command.referenceUsername: dbuser

# Enter the password for your source database
#liquibase.command.referencePassword: letmein

#liquibase.command.contexts: main
#liquibase.command.update.contexts: main

# Logging Configuration
# logLevel controls the amount of logging information generated. If not set, the default logLevel is INFO.
# Valid values, from least amount of logging to most, are:
#   OFF, ERROR, WARN, INFO, DEBUG, TRACE, ALL
# If you are having problems, setting the logLevel to DEBUG and re-running the command can be helpful.
# logLevel: DEBUG

# The logFile property controls where logging messages are sent. If this is not set, then logging messages are
# displayed on the console. If this is set, then messages will be sent to a file with the given name.
# logFile: liquibase.log
liquibase.showBanner: false

#### Liquibase Pro Key Information ####
# Learn more, contact support, or get or renew a Pro Key at https://www.liquibase.com/trial
# liquibase.licenseKey:

#### Liquibase Hub Information ####
# Liquibase Hub is a free secure SaaS portal providing status reporting, monitoring & insights
# into your Liquibase database release automation.
# https://hub.liquibase.com

## Add your free Hub API key here
# liquibase.hub.apikey:
# liquibase.hub.mode:all

##  Get documentation at docs.liquibase.com       ##
##  Get certified courses at learn.liquibase.com  ##
##  Get support at liquibase.com/support         ##

liquibase.hub.mode=off
