using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class SvmApi : TypeApi
{
    public SvmApi(OntapApi client) : base(client)
    {
    }

    public async Task<List<Svm>> GetSvms()
    {
        return await Client.GetResources<Svm>(UrlBuilder.ListUrl("/svm/svms"));
    }

    public async Task<List<Svm>> GetSvms(List<Guid> svmIds)
    {
        var svmIdsQuery = string.Join(",", svmIds);
        return await Client.GetResources<Svm>(UrlBuilder.ListUrl("/svm/svms").AddQueryParam("uuid", svmIdsQuery));
    }

    public async Task<Svm> GetSvm(Guid uuid)
    {
        var url = new UrlBuilder("/svm/svms/%uuid%")
            .AddPathParam("uuid", uuid.ToString())
            .AddFields("**");
        return await Client.GetResource<Svm>(url);
    }

    public async Task<Svm> GetSvm(string name)
    {
        var url = new UrlBuilder("/svm/svms")
            .AddFields("**")
            .AddQueryParam("name", name);
        return await Client.SearchResource<Svm>(url);
    }

    public async Task<List<Svm>> GetSvmByIpSpaceName(string ipSpaceName)
    {
        return await Client.GetResources<Svm>(UrlBuilder.ListUrl("/svm/svms").AddQueryParam("ipspace.name", ipSpaceName));
    }

    public async Task<ListResponse<Svm>> CreateSvm(string name, Guid ipspaceUuid, Aggregate aggregate)
    {
        return await Client.CreateResourceAsync(new UrlBuilder("/svm/svms"), new Svm(name.ToLowerInvariant(), ipspaceUuid, aggregate));
    }

    public async Task<ListResponse<Svm>> CreateSvm(string name, Ipspace ipspace, Aggregate aggregate)
    {
        return await CreateSvm(name.ToLowerInvariant(), ipspace.Uuid, aggregate);
    }

    public async Task UpdateSvm(string name, Guid uuid)
    {
        await Client.PatchResource(UrlBuilder.ResourceUrl("/svm/svms", uuid), new UpdateSvm(name.ToLowerInvariant()));
    }

    public async Task<Job> DeleteSvm(Guid uuid)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/svm/svms", uuid));
    }

    public async Task<List<IpInterface>> GetSvmInterfaces()
    {
        return await Client.GetResources<IpInterface>(new UrlBuilder("/network/ip/interfaces").AddFields().AddQueryParam("scope", "svm"));
    }

    public async Task<List<IpInterface>> GetSvmInterfaces(Guid svmId)
    {
        return await Client.GetResources<IpInterface>(new UrlBuilder("/network/ip/interfaces").AddFields().AddQueryParam("scope", "svm").AddQueryParam("svm.uuid", svmId));
    }

    public async Task<IpInterface> CreateSvmInterface(string name, Guid broadcastDomain, string ip, string netmask, Guid svmId, Guid homeNode)
    {
        var body = new IpInterface(name.ToLowerInvariant(), broadcastDomain, ip, netmask, homeNode, svmId);
        return await Client.CreateResource(new UrlBuilder("/network/ip/interfaces"), body);
    }

    public async Task<IpInterface> CreateSvmInterface(string name, Guid broadcastDomain, string ip, string netmask, Guid svmId)
    {
        var body = new IpInterface(name.ToLowerInvariant(), broadcastDomain, ip, netmask, Guid.Empty, svmId);
        return await Client.CreateResource(new UrlBuilder("/network/ip/interfaces"), body);
    }

    public async Task<IpInterface> CreateSvmInterface(string name, BroadcastDomain broadcastDomain, string ip,
        string netmask, Guid svmId)
    {
        return await CreateSvmInterface(name.ToLowerInvariant(), broadcastDomain.Uuid, ip, netmask, svmId);
    }

    public async Task UpdateSvmInterface(Guid uuid, string name, Guid broadcastDomain, string ip, string netmask, Guid svmId, Guid homeNode)
    {
        var body = new IpInterface(name.ToLowerInvariant(), broadcastDomain, ip, netmask, homeNode, svmId);

        await Client.PatchResource(UrlBuilder.ResourceUrl("/network/ip/interfaces", uuid), body);
    }

    public async Task UpdateSvmInterfaceName(Guid uuid, string name)
    {
        var body = new IpInterfaceNameUpdate(name.ToLowerInvariant());

        await Client.PatchResource(UrlBuilder.ResourceUrl("/network/ip/interfaces", uuid), body);
    }

    public async Task UpdateSvmIP(Guid uuid, string ip, string netmask)
    {
        var body = new IpInterfaceUpdate(ip, netmask);

        await Client.PatchResource(UrlBuilder.ResourceUrl("/network/ip/interfaces", uuid), body);
    }

    public async Task<Job> DeleteSvmInterface(Guid uuid)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/network/ip/interfaces", uuid));
    }

    public async Task<List<Route>> GetRoutes()
    {
        return await Client.GetResources<Route>(UrlBuilder.ListUrl("/network/ip/routes"));
    }

    public async Task<Route> CreateRoute(string gateway, Guid svmId)
    {
        var body = new Route(gateway, svmId);
        return await Client.CreateResource(new UrlBuilder("/network/ip/routes"), body);
    }

    public async Task DeleteRoute(Guid uuid)
    {
        var url = UrlBuilder.ResourceUrl("/network/ip/routes", uuid);
        await Client.DeleteResource(url);
    }

    public async Task<List<Dns>> GetDns()
    {
        return await Client.GetResources<Dns>(UrlBuilder.ListUrl("/name-services/dns"));
    }

    public async Task<Dns> CreateDns(List<string> domains, List<string> servers, Guid svmId)
    {
        var body = new Dns(domains, servers, svmId);
        return await Client.CreateResource(new UrlBuilder("/name-services/dns"), body);
    }

    public async Task DeleteDns(Guid uuid)
    {
        await Client.DeleteResource(UrlBuilder.ResourceUrl("/name-services/dns", uuid));
    }

    public async Task ModifyDns(Guid svmId, List<string> domains, List<string> servers)
    {
        var body = new Dns(domains, servers);
        await Client.PatchResource(UrlBuilder.ResourceUrl("/name-services/dns", svmId), body);
    }

    public async Task<ExportPolicy> GetExportPolicy(Guid svmId, string name)
    {
        return await Client.SearchResource<ExportPolicy>(
            new UrlBuilder("/protocols/nfs/export-policies")
                .AddFields()
                .AddQueryParam("name", name)
                .AddQueryParam("svm.uuid", svmId));
    }

    public async Task<ExportPolicyRule> CreateAllowAllExportPolicyRule(long policy, string clients)
    {
        var body = new ExportPolicyRule("any", "any", clients);
        return await Client.CreateResource(
            new UrlBuilder("/protocols/nfs/export-policies/%policyId%/rules").AddPathParam("policyId", policy.ToString()),
            body);
    }

    public async Task<ServicePolicy> GetServicePolicy(Guid svmId)
    {
        return await Client.SearchResource<ServicePolicy>(UrlBuilder.ListUrl("/network/ip/service-policies")
            .AddQueryParam("svm.uuid", svmId)
            .AddQueryParam("scope", "svm")
            .AddQueryParam("name", "default-data-files"));
    }

    public async Task UpdateServicePolicy(ServicePolicy policy)
    {
        var body = new ServicePolicy(policy.Name, policy.Services);
        var response = await Client.ExecutePatchRequest(new UrlBuilder("/network/ip/service-policies/%uuid%").AddPathParam("uuid", policy.Uuid), body);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            throw new OntapApiException($"Unable to patch service policy: {content}");
        }
    }
}