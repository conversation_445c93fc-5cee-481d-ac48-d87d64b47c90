﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class RemoteDesktopConnectionInitiateDTO
    {
        [JsonPropertyName("session_private_agent_information_encrypted_base64")]
        public string SessionPrivateAgentInformationEncryptedBase64 { get; set; }

        [JsonPropertyName("session_user_email_address")]
        public string SessionUserEMailAddress { get; set; }

        [JsonPropertyName("session_user_machine_ip_address")]
        public string SessionUserMachineIPAddress { get; set; }

        [JsonPropertyName("session_started_at_unix_timestamp")]
        public long SessionStartedAtUnixTimestamp { get; set; }
    }
}