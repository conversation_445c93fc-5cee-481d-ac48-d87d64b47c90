﻿using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication.DTOs;

namespace MyAdaptiveCloud.Services.Authentication
{
    /// <summary>
    ///     Service for authorization checks.
    ///     This service must be consumed by Authorize filters in the API only.
    ///     The methods implemented here must be read-only, non-tracking and must not return full entities.
    ///     Also, it must not inject any other services or repositories.
    /// </summary>
    public interface IEntityAuthorizationService
    {
        IEnumerable<int> GetDescendantOrganizationsId(int organizationId);

        Task<int?> GetActivationKeyOrganizationId(int activationKeyId);

        Task<int?> GetAuthenticationKeyPersonId(int authenticationKeyId);

        Task<int?> GetAdaptiveCloudOrganizationId(string acId);

        Task<bool> GetCloudInfraMappingIdByOrganizationId(int organizationId);

        Task<int?> GetAgentOrganizationId(int agentId);

        Task<IEnumerable<int>> GetAgentsOrganizationId(IEnumerable<int> agentIds);

        Task<AuthorizePersonDTO> GetPerson(int personId);

        /// <summary>
        ///     Gets the list of organization ids where the supplied person is a member (either user or contact)
        /// </summary>
        Task<List<int>> GetPersonOrganizationIds(int personId);

        Task<bool> RoleIsAvailableToOrganization(int roleId, int organizationId);

        Task<bool> RoleIsRestricted(int roleId);

        Task<bool> BillingCompanyExists(int companyId);

        /// <summary>
        ///     Gets the organizationId linked to the supplied contact.
        /// </summary>
        /// <param name="contactId">The ID of the entity record in the user_organization table.</param>
        /// <returns></returns>
        Task<int?> GetContactOrganizationId(int contactId);

        Task<bool> OrganizationIsPartner(int organizationId);

        Task<int?> GetDeviceAlertRuleOrganizationId(int deviceAlertRuleId);

        Task<int?> GetDeviceFolderOrganizationId(int deviceFolderId);

        Task<List<int>> GetDeviceFoldersOrganizationId(List<int> deviceFolderIds);

        Task<List<int>> GetDevicesControlsOrganizationId(List<int> devicesControlIds);

        Task<int?> GetScheduleDowntimeOrganizationId(int deviceFolderId);

        Task<int?> GetEscalationChainOrganizationId(int escalationChainId);

        Task<int?> GetAdaptiveCloudDriveFileOrganizationId(int fileId);

        Task<AuthorizeAdaptiveCloudDriveFolderDTO> GetAdaptiveCloudDriveFolder(int folderId);

        Task<int?> GetFileServerOrganizationId(int fileServerId);

        Task<int?> GetInvoiceOrganizationId(int invoiceId);

        Task<int?> GetNotificationOrganizationId(int notificationId);

        /// <summary>
        /// Grants authorization to a user from a descendant organization of Organization A to access the policy or schedule of Organization A.
        /// This method should only be used in the PolicyFromAncestorAuthorizeFilter and ScheduleFromAncestorAuthorizeFilter. If you plan to use it outside those filters, be very aware of security implications of what you are trying to do. 
        /// </summary>
        /// <returns>Returns true when <paramref name="descendantOrganizationId"/> is within the descendants hierarchy of <paramref name="organizationId"/> </returns>
        public bool IsOrganizationWithinOrganizationHierarchy(int organizationId, int descendantOrganizationId);

        Task<int?> GetPolicyOrganizationId(int policyId);

        Task<AuthorizeRoleDTO> GetRoleOrganizationId(int roleId);

        Task<int?> GetScheduleOrganizationId(int scheduleId);

        Task<int?> GetServiceOrganizationId(int serviceId);

        Task<int?> GetUserRoleOrganizationId(int userRoleId);

        Task<IEnumerable<int>> GetUserRoleOrganizationIds(List<int> userRoleIds);

        Task<AuthorizeAgentActionByVersionServiceDTO> AgentActionByVersionService(int agentId);

        bool HasUserRolesByOrg(int userId, int organizationId, bool includeUnapprovedRoles);

        Task<bool> OrganizationExists(int organizationId, bool includeInactiveOrganizations);

        /// <summary>
        ///     Gets the organizationId linked to the supplied user.
        /// </summary>
        /// <param name="userId">The ID of the entity record in the user_organization table.</param>
        /// <returns></returns>
        Task<int?> GetUserOrganizationId(int userId);

        /// <summary>
        ///     Gets the organizationId linked to the supplied API User.
        /// </summary>
        /// <param name="apiUserId">The ID of the entity record in the user_organization table.</param>
        /// <returns></returns>
        Task<int?> GetApiUserOrganizationId(int apiUserId);

        Task<int?> GetDeviceControlOrganizationId(int deviceControlId);

        /// <summary>
        /// Check invitation code exists.
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<bool> InvitationCodeExists(string code);

        /// <summary>
        /// Gets invitationCodeId
        /// </summary>
        /// <param name="invitationCodeId">The ID of the table</param>
        /// <returns></returns>
        Task<int?> GetOrganizationIdInvitationCode(int invitationCodeId);

        /// <summary>
        ///     Verifies that at least one service matching the supplied ids and organization
        /// </summary>
        Task<bool> HasTotalTechService(List<int> serviceIds, int organizationId);

        /// <summary>
        ///     Verifies that the supplied document is in the folder configured as TotalTech
        /// </summary>
        Task<bool> DocumentIsInTotalTechFolder(int documentId, int folderId);

        /// <summary>
        ///     Determines if the device is controlled by a user with any of the specified roles.
        /// </summary>
        Task<bool> IsDeviceControlledByUserWithRoles(int agentId, int userId, DeviceControlRoleEnum[] deviceControlRoles);


        /// <summary>
        ///     Verifies that the supplied organization is inactive
        /// </summary>
        Task<bool> IsOrganizationInactive(int organizationId);

        Task<bool> ValidateUnsubscribeMessage(string subscriberEmail, string messageQueueItemMessageId);

        /// <summary>
        ///     Used in profile endpoints so it assumes the person is operating on themselves.
        ///     Verifies that the person has is member of the supplied organization and that the 
        ///     service is linked to the supplied organization
        /// </summary>
        /// <returns></returns>
        Task<bool> HasPersonAccessToService(int organizationId, int serviceId, int personId);

        /// <summary>
        ///     Verifies that the supplied document is within the folder hierarchy starting with the folder configured as Partner Resource Hub as root
        /// </summary>
        Task<bool> DocumentIsInPartnerResourceHubFolderHierarchy(int documentId, int folderId);

        Task<int?> GetReportRequestOrganizationId(int reportId);

        Task<bool> HasReportExecutionId(int reportExecutionId);
    }
}