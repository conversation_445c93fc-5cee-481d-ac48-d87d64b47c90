﻿namespace MyAdaptiveCloud.Services.Authentication.DTOs
{
    public class AuthorizeAgentActionByVersionServiceDTO : IComparable<AuthorizeAgentActionByVersionServiceDTO>
    {
        public int AgentServiceMajor { get; set; }

        public int AgentServiceMinor { get; set; }

        public int AgentServiceBuild { get; set; }

        public int CompareTo(AuthorizeAgentActionByVersionServiceDTO other)
        {
            if (other == null) return 1;

            int majorCompare = AgentServiceMajor.CompareTo(other.AgentServiceMajor);
            if (majorCompare != 0) return majorCompare;

            int minorCompare = AgentServiceMinor.CompareTo(other.AgentServiceMinor);
            if (minorCompare != 0) return minorCompare;

            return AgentServiceBuild.CompareTo(other.AgentServiceBuild);
        }

        public static bool operator >(AuthorizeAgentActionByVersionServiceDTO a, AuthorizeAgentActionByVersionServiceDTO b) => a.Compare<PERSON>o(b) > 0;

        public static bool operator <(AuthorizeAgentActionByVersionServiceDTO a, AuthorizeAgentActionByVersionServiceDTO b) => a.CompareTo(b) < 0;

        public static bool operator >=(AuthorizeAgentActionByVersionServiceDTO a, AuthorizeAgentActionByVersionServiceDTO b) => a.CompareTo(b) >= 0;

        public static bool operator <=(AuthorizeAgentActionByVersionServiceDTO a, AuthorizeAgentActionByVersionServiceDTO b) => a.CompareTo(b) <= 0;
    }
}