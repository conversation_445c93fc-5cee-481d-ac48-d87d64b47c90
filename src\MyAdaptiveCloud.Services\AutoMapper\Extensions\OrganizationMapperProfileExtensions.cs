﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Organization;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Policies;

namespace MyAdaptiveCloud.Services.AutoMapper.Extensions
{
    public static class OrganizationMapperProfileExtensions
    {
        public static OrganizationFolderDTO MapToOrganizationFolderDTO(this IMapper mapper, Data.MyAdaptiveCloud.Organization organization,
            OrganizationPolicyConfiguration organizationPolicyConfiguration) => mapper.Map<OrganizationFolderDTO>(Tuple.Create(organization, organizationPolicyConfiguration));

        public static OrganizationFolderDTO MapToOrganizationFolderDTO(this IMapper mapper, OrganizationHierarchy organization,
            OrganizationPolicyConfiguration organizationPolicyConfiguration) => mapper.Map<OrganizationFolderDTO>(Tuple.Create(organization, organizationPolicyConfiguration));
    }
}