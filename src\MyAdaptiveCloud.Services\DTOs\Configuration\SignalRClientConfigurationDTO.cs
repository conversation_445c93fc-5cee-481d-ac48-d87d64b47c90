namespace MyAdaptiveCloud.Services.DTOs.Configuration
{
    public class SignalRClientConfigurationDTO
    {
        public const string Category = "Agent SignalR Command Client Configuration";

        public string BaseUrl { get; set; }

        public string ApiKey { get; set; }

        public string RemoteCommandsUrlSegment { get; set; }

        public string UploadInstallerUrlSegment { get; set; }

        public string DefaultInstallerFilePath { get; set; }

        public string AgentUpgradeUrlSegment { get; set; }
    }
}