﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.ScheduledDowntime;
using MyAdaptiveCloud.Services.DTOs.DeviceFolder;
using MyAdaptiveCloud.Services.DTOs.ScheduledDowntime;

namespace MyAdaptiveCloud.Services.AutoMapper.ScheduledDowntime
{
    public class ScheduledDowntimeMappingProfile : Profile
    {
        public ScheduledDowntimeMappingProfile()
        {
            CreateMap<ScheduledDowntimeDTO, Data.MyAdaptiveCloud.ScheduledDowntime.ScheduledDowntime>()
                .ForMember(dest => dest.Organization, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduledDowntimeId, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Creator, opt => opt.Ignore())
                .ForMember(dest => dest.Updater, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduledDowntimeAlertTypes, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduledDowntimeDevices, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduledDowntimeFolders, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduledDowntimeOrganizations, opt => opt.Ignore());

            CreateMap<Data.MyAdaptiveCloud.ScheduledDowntime.ScheduledDowntime, ScheduledDowntimeDTO>()
                .ForMember(dest => dest.CreatedByName,
                       opt => opt.MapFrom(src => $"Created By: {src.Creator.FullName} <{src.Creator.Email}>"))
                .ForMember(dest => dest.UpdatedByName,
                       opt => opt.MapFrom(src => src.UpdatedBy != null ? $"Last Edited By: {src.Updater.FullName} <{src.Updater.Email}>" : ""))
                .ForMember(dest => dest.Devices, opt => opt.Ignore())
                .ForMember(dest => dest.MetricsToShow, opt => opt.Ignore())
                .ForMember(dest => dest.MatchedDevices, opt => opt.Ignore())
                .ForMember(dest => dest.Metrics, opt => opt.Ignore());


            CreateMap<ScheduledDowntimeDevice, AffectedDeviceDTO>()
                .ForMember(dest => dest.FolderId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.AgentId))
                .ForMember(dest => dest.IncludeSubfolders, opt => opt.Ignore());

            CreateMap<ScheduledDowntimeFolder, AffectedDeviceDTO>()
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.IncludeSubfolders, opt => opt.MapFrom(src => src.IncludeSubfolders));

            CreateMap<ScheduledDowntimeOrganization, AffectedDeviceDTO>()
                .ForMember(dest => dest.FolderId, opt => opt.Ignore())
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.IncludeSubfolders, opt => opt.MapFrom(src => src.IncludeSubfolders));

            CreateMap<ScheduledDowtimeStatuses, Data.MyAdaptiveCloud.ScheduledDowntime.ScheduledDowntimeStatusEnum>();
        }
    }
}
