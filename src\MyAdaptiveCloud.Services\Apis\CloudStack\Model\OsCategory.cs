using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class OsCategory
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
    }

    public class ListOsCategoriesResponse : CloudStackListResponse<OsCategory>
    {
        [JsonPropertyName("oscategory")]
        public override List<OsCategory> Items { get; set; } = new List<OsCategory>();
    }
}