﻿using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.DeviceFolder;
using MyAdaptiveCloud.Services.DTOs.Organization;

namespace MyAdaptiveCloud.Services.AutoMapper.Extensions
{
    public static class QueryableExtensions
    {
        public static async Task<List<HierarchyFolderDTO>> ToHierarchyFolderDTOsAsync(this IQueryable<DeviceFolder> queryable)
        {
            var folderList = await queryable.AsNoTracking()
                .Select(s => new HierarchyFolderDTO
                {
                    OrganizationId = s.OrganizationId,
                    FolderId = s.FolderId,
                    ParentFolderId = s.ParentFolderId,
                    Name = s.Name
                })
                .ToListAsync();

            var folderDict = folderList.ToDictionary(f => f.FolderId);

            foreach (var folder in folderList)
            {
                if (folder.ParentFolderId.HasValue && folderDict.TryGetValue(folder.ParentFolderId.Value, out var parent))
                {
                    folder.ParentFolder = parent;
                }
            }

            return folderList;
        }

        public static async Task<List<BaseOrganizationDTO>> ToBaseOrganizationDTOsAsync(this IQueryable<Data.MyAdaptiveCloud.Organization> queryable)
        {
            return await queryable.AsNoTracking()
                .Select(s => new BaseOrganizationDTO
                {
                    OrganizationId = s.OrganizationId,
                    ParentOrganizationId = s.ParentOrganizationId
                })
                .ToListAsync();
        }
    }
}