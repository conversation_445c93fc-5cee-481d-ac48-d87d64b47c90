﻿using MyAdaptiveCloud.Services.DTOs.Alerts;
using MyAdaptiveCloud.Services.DTOs.DeviceThresholds;

namespace MyAdaptiveCloud.Services.DTOs.AlertRules
{
    public class AlertRulesDTO
    {
        public int AlertRuleId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public int Action { get; set; }

        public string ActionName { get; set; }

        public int OrganizationId { get; set; }

        public int? EscalationChainId { get; set; }

        public int Order { get; set; }

        public virtual List<DeviceAlertRuleCriteriaDTO> AlertRulesCriteria { get; set; } = new List<DeviceAlertRuleCriteriaDTO>();

        public virtual List<AlertRulesDeviceDTO> Devices { get; set; } = new List<AlertRulesDeviceDTO>();

        public string CreatedByName { get; set; }
        public string UpdatedByName { get; set; }

        public DateTimeOffset CreatedOn { get; set; }
        public DateTimeOffset? UpdatedOn { get; set; }

        public bool Enabled { get; set; }

        public int MatchedDevices { get; set; }

        public bool KeepSearchingAlertRulesInChildrenOrganizations { get; set; }

        public DeviceAlertRuleCriteriaDTO GetMetric(DeviceAlertTypeDTO metric)
        {
            return AlertRulesCriteria.FirstOrDefault(a => a.DeviceAlertTypeId == metric.DeviceAlertTypeId);
        }

        public AlertRulesDTO AddRulesCriteria(DeviceAlertRuleCriteriaDTO deviceAlertRuleDTO)
        {
            AlertRulesCriteria.Add(deviceAlertRuleDTO);
            return this;
        }

        public IEnumerable<AlertRulesDeviceDTO> GetFolders()
        {
            return Devices.Where(d => d.ItemType == DeviceAlertItemTypeDTO.Folder);
        }

        public IEnumerable<AlertRulesDeviceDTO> GetDevices()
        {
            return Devices.Where(d => d.ItemType == DeviceAlertItemTypeDTO.Device);
        }

        public IEnumerable<AlertRulesDeviceDTO> GetOrganizations()
        {
            return Devices.Where(d => d.ItemType == DeviceAlertItemTypeDTO.Organization || d.ItemType == DeviceAlertItemTypeDTO.PartnerOrganization);
        }
    }
}