﻿using MyAdaptiveCloud.Services.DTOs.AlertRules;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class DeviceAlertDTO
    {
        public int AgentId { get; set; }

        public int DeviceAlertId { get; set; }

        public string AlertType { get; set; }

        public string AlertStatus { get; set; }

        public string AcknowledgedBy { get; set; }
        
        public DateTimeOffset? AcknowledgedDate { get; set; }
        
        public string Notes { get; set; }

        public AlertThresholdLevelEnumDTO AlertThresholdLevel { get; set; }

        public int AlertTypeId { get; set; }

        public bool InScheduledDownTime { get; set; }

        public DateTimeOffset? ScheduleDowntimeEndDate { get; set; }
    }
}
