﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.AutoMapper.Extensions;
using MyAdaptiveCloud.Services.DTOs.DeviceAlerts.AlertGeneration;
using MyAdaptiveCloud.Services.DTOs.Organization;

namespace MyAdaptiveCloud.Services.AutoMapper.Organization
{
    public class OrganizationMapperProfile : Profile
    {
        public OrganizationMapperProfile()
        {
            CreateMap<Data.MyAdaptiveCloud.Organization, OrganizationHierarchyWithDistance>()
                .ForMember(model => model.Distance, option => option.Ignore());
            CreateMap<OrganizationHierarchyWithDistance, Data.MyAdaptiveCloud.Organization>()
                .ForMember(model => model.ParentOrganization, option => option.Ignore())
                .ForMember(dest => dest.OrganizationMappings, opt => opt.Ignore())
                .ForMember(dest => dest.DataProtectionCredentials, opt => opt.Ignore());
            CreateMap<OrganizationHierarchy, OrganizationListItemDTO>().IgnoreViewModelUserActions()
                .ForMember(dest => dest.CompanyShortName, option => option.Ignore())
                .AfterMap((src, dest) => dest.CompanyShortName = string.Empty);
            CreateMap<OrganizationHierarchy, DeletedOrganizationDTO>().IgnoreViewModelUserActions()
                .ForMember(dest => dest.ParentIsActive, option => option.Ignore())
                .ForMember(dest => dest.CanCreate, option => option.Ignore())
                .ForMember(dest => dest.CanDelete, option => option.Ignore())
                .ForMember(dest => dest.CanView, option => option.Ignore())
                .ForMember(dest => dest.CompanyShortName, option => option.Ignore());

            CreateMap<Data.MyAdaptiveCloud.Organization, OrganizationForAlertsDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.ParentOrganizationId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

            CreateMap<OrganizationForAlertsDTO, Data.MyAdaptiveCloud.Organization>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.ParentOrganizationId, opt => opt.MapFrom(src => src.ParentId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.IsActive, opt => opt.Ignore())
                .ForMember(dest => dest.AllowSubOrg, opt => opt.Ignore())
                .ForMember(dest => dest.AllowWhiteLabel, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedDate, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedDate, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationMappings, opt => opt.Ignore())
                .ForMember(dest => dest.DataProtectionCredentials, opt => opt.Ignore())
                .ForMember(dest => dest.IsVerified, opt => opt.Ignore())
                .ForMember(dest => dest.IsPartner, opt => opt.Ignore())
                .ForMember(dest => dest.ParentOrganization, opt => opt.Ignore());

            CreateMap<Data.MyAdaptiveCloud.Organization, BaseOrganizationDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.ParentOrganizationId, opt => opt.MapFrom(src => src.ParentOrganizationId));
        }
    }
}