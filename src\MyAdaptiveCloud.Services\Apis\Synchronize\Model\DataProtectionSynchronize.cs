﻿using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Apis.Synchronize.Model
{
    [ApplicationType(Applications.DataProtection)]
    public class DataProtectionSynchronize: ISynchronize
    {
        public async Task UpdateOrganizationName(int organizationId, string name)
        {
            await Task.CompletedTask;
        }

        public async Task DisconnectApplication(int organizationId)
        {
            // TODO Should we remove the Tenant?
            await Task.CompletedTask;
        }
    }
}
