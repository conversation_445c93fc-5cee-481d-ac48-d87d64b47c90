namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Ip
{
    public string Address { get; set; }
    public string Netmask { get; set; }
    
    public Ip() {}

    public Ip(string address, string netmask)
    {
        Address = address;
        Netmask = netmask;
    }
}

public class IpInterfaceLocation
{
    public BroadcastDomain BroadcastDomain { get; set; }
    public OntapObject HomeNode { get; set; }

    public IpInterfaceLocation()
    {
    }

    public IpInterfaceLocation(Guid broadcastDomain, Guid node)
    {
        BroadcastDomain = new BroadcastDomain(broadcastDomain);
        if (node != Guid.Empty)
        {
            HomeNode = new OntapObject(node);
        }
    }
}

public class IpInterface : OntapObject
{
    public Ip Ip { get; set; }
    public Ipspace Ipspace { get; set; }
    public IpInterfaceLocation Location { get; set; }
    public Svm Svm { get; set; }

    public IpInterface()
    {
    }

    public IpInterface(string name, Guid broadcastDomain, string ip, string netmask, Guid node, Guid svm)
    {
        Name = name;
        Ip = new Ip(ip, netmask);
        Location = new IpInterfaceLocation(broadcastDomain, node);
        Svm = new Svm(svm);
        // ServicePolicy = "default-management";
    }   

    public override string ToString()
    {
        return $"Interface: {Name} IP: {Ip.Address} Netmask: {Ip.Netmask} SVM: {Svm}";
    }
}

public class IpInterfaceUpdate {

    public IpInterfaceUpdate()
    {
    }
    public Ip Ip { get; set; }

    public IpInterfaceUpdate(string ip, string netmask)
    {
        Ip = new Ip(ip, netmask);
    }
}

public class IpInterfaceNameUpdate
{

    public IpInterfaceNameUpdate()
    {
    }
    public string Name { get; set; }

    public IpInterfaceNameUpdate(string name)
    {
        Name = name;
    }
}