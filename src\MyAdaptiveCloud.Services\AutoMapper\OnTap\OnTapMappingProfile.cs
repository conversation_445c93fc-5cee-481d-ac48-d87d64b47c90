﻿using AutoMapper;
using MyAdaptiveCloud.Services.Apis.Ontap.Model;
using MyAdaptiveCloud.Services.DTOs.Ontap;
using FileInfo = MyAdaptiveCloud.Services.Apis.Ontap.Model.FileInfo;

namespace MyAdaptiveCloud.Services.AutoMapper.OnTap
{
    public class OnTapMappingProfile : Profile
    {
        public OnTapMappingProfile()
        {
            CreateMap<FileInfo, DirectoryDTO>()
                .ForMember(dest => dest.Children, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Path, opt => opt.MapFrom(src => src.Path));

            CreateMap<UserOrGroupToShareDTO, CifsShareAcl>()
                .ForMember(dest => dest.Permission, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.Ignore());
        }
    }
}