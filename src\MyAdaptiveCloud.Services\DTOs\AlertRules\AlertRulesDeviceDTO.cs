﻿using MyAdaptiveCloud.Services.DTOs.Alerts;

namespace MyAdaptiveCloud.Services.DTOs.AlertRules
{
    public class AlertRulesDeviceDTO
    {
        public int AlertRuleDeviceId { get; set; }
        public int ItemId { get; set; }
        public DeviceAlertItemTypeDTO ItemType { get; set; }
        public bool IncludeSubfolders { get; set; }
        public string Path { get; set; }
        public string Name { get; set; }
        public int FolderId { get; set; }
    }
}
