﻿using MyAdaptiveCloud.Services.Apis.AgentRemote.Model;

namespace MyAdaptiveCloud.Services.Apis.AgentRemote
{
    public interface IAgentRemoteApi
    {
        Task<RemoteDesktopInitiateResponse> RemoteDesktopInitiate(string agentUuId, string agentPublicKeyBase64);
        Task<RemoteDesktopStatusResponse> RemoteDesktopStatus(string agentUuId);
        Task TerminateConnectionForAgent(string agentUuId);
    }
}
