﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Files;

namespace MyAdaptiveCloud.Services.AutoMapper.FolderAdministration
{
    public class FolderMapperProfile : Profile
    {
        public FolderMapperProfile()
        {
            CreateMap<FolderHierarchyList, FileFolderDTO>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(o => o.Name))
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(o => o.FolderId))
                .ForMember(dest => dest.ParentFolderId, opt => opt.MapFrom(o => o.ParentFolderId))
                .ForMember(dest => dest.Hidden, opt => opt.MapFrom(o => o.Hidden))
                .ForMember(dest => dest.Children, opt => opt.Ignore());

            CreateMap<AdaptiveCloudDriveFolder, FileFolderDTO>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(o => o.Name))
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(o => o.FolderId))
                .ForMember(dest => dest.ParentFolderId, opt => opt.MapFrom(o => o.ParentFolderId))
                .ForMember(dest => dest.Hidden, opt => opt.MapFrom(o => o.Hidden))
                .ForMember(dest => dest.Children, opt => opt.Ignore());
        }
    }
}