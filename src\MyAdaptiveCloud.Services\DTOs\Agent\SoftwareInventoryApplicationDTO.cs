﻿namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class SoftwareInventoryApplicationDTO
    {
        public string HostName { get; set; }

        public string Version { get; set; }

        public DateTimeOffset? InstalledOn { get; set; }

        public bool HasUninstall { get; set; }

        public int SoftwareInventoryStatus { get; set; }

        public bool CanViewUninstallSoftwareInventory { get; set; }

        public int AgentId { get; set; }
    }
}