namespace MyAdaptiveCloud.Services.Apis.ConnectWise.Model
{
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public string Type { get; set; }
        public BillingStatusRef Status { get; set; }
        public CompanyRef Company { get; set; }
        public string ApplyToType { get; set; }
        public BillingTermsRef BillingTerms { get; set; }
        public DateTime? Date { get; set; }
        public DateTime? DueDate { get; set; }
        public double Total { get; set; }
        public double Payments { get; set; }
        public double Balance { get; set; }
        public TicketRef Ticket { get; set; }
        public ProjectRef Project { get; set; }
        public ProjectPhaseRef Phase { get; set; }
        public SalesOrderRef SalesOrder { get; set; }
        public AgreementRef Agreement { get; set; }
    }
    public class BillingStatusRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
    public class BillingTermsRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
    public class TicketRef
    {
        public int Id { get; set; }
        public string Summary { get; set; }
    }
    public class ProjectRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
    public class ProjectPhaseRef
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
    public class SalesOrderRef
    {
        public int Id { get; set; }
        public string Identifier { get; set; }
    }
}