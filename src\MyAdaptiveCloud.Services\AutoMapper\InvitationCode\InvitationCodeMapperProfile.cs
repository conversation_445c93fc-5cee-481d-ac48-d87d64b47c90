﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Invitation;

namespace MyAdaptiveCloud.Services.AutoMapper.InvitationCode
{
    public class InvitationCodeMapperProfile : Profile
    {

        public InvitationCodeMapperProfile()
        {
            CreateMap<Data.MyAdaptiveCloud.InvitationCode, InvitationCodeDto>()
                   .ForMember(dest => dest.OrganizationName, opt => opt.MapFrom(o => o.Organization.Name))
                   .ForMember(dest => dest.Roles, opt => opt.MapFrom(o => o.Roles.Select(r => r.Role).OrderBy(r => r.Name).ToArray()))
                   .ForMember(dest => dest.Created, opt => opt.MapFrom(o => o.CreatedOn))
                   .ForMember(dest => dest.Expiration, opt => opt.MapFrom(o => o.ExpirationDate))
                   .ForMember(dest => dest.UsedCount, opt => opt.MapFrom(o => o.UsedCount))
                   .ForMember(dest => dest.IsExpired, opt => opt.Ignore())
                   .ForMember(dest => dest.IsCloseToExpire, opt => opt.Ignore())
                   .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(o => o.Creator))
                   .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(o => o.OrganizationId))
                   .ForMember(dest => dest.InvitationCodeId, opt => opt.MapFrom(o => o.InvitationCodeId))
                   .ForMember(dest => dest.UpdatedBy, opt => opt.MapFrom(src => src.Updater))
                   .ForMember(dest => dest.UpdatedOn, opt => opt.MapFrom(src => src.UpdatedOn != null ? src.UpdatedOn : null));
        }
    }
}