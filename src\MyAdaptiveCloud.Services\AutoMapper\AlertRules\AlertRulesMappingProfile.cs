﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.AlertRules;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Alerts;
using MyAdaptiveCloud.Services.DTOs.AlertRules;
using MyAdaptiveCloud.Services.DTOs.DeviceFolder;
using MyAdaptiveCloud.Services.DTOs.DeviceThresholds;

namespace MyAdaptiveCloud.Services.AutoMapper.AlertRules
{
    public class AlertRulesMappingProfile : Profile
    {
        public AlertRulesMappingProfile()
        {
            CreateMap<DeviceAlertRuleCriteria, DeviceAlertRuleCriteriaDTO>()
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => src.CreatedOn))
                .ForMember(dest => dest.Enabled, opt => opt.Ignore())
                .ForMember(dest => dest.DisplayOrder, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore());

            CreateMap<DeviceAlertRuleCriteriaDTO, DeviceAlertRuleCriteria>()
                .ForMember(dest => dest.DeviceAlertRuleComparer, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => src.CreatedOn))
                .ForMember(dest => dest.DeviceAlertRule, opt => opt.Ignore());

            CreateMap<DeviceAlertRulesDevices, AlertRulesDeviceDTO>()
                .ForMember(dest => dest.Path, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.FolderId, opt => opt.Ignore())
                .ForMember(dest => dest.AlertRuleDeviceId, opt => opt.MapFrom(src => src.DeviceAlertRuleDeviceId))
                .ForMember(dest => dest.ItemType, opt => opt.MapFrom(src => (DeviceAlertItemTypeEnum)Enum.Parse(typeof(DeviceAlertItemTypeEnum), src.ItemType)));

            CreateMap<AlertRulesDeviceDTO, DeviceAlertRulesDevices>()
                .ForMember(dest => dest.DeviceAlertRuleId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRuleDeviceId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRule, opt => opt.Ignore())
                .ForMember(dest => dest.ItemType, opt => opt.MapFrom(src => src.ItemType.ToString()));

            CreateMap<AlertRulesDTO, DeviceAlertRule>()
                .ForMember(dest => dest.DeviceAlertRuleId, opt => opt.MapFrom(src => src.AlertRuleId))
                .ForMember(dest => dest.Organization, opt => opt.Ignore())
                .ForMember(dest => dest.EscalationChain, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRuleId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRuleAction, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Order, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRuleActionId, opt => opt.MapFrom(src => src.Action))
                .ForMember(dest => dest.Enabled, opt => opt.MapFrom(src => src.Enabled))
                .ForMember(dest => dest.Creator, opt => opt.Ignore())
                .ForMember(dest => dest.Updater, opt => opt.Ignore())
                .ForMember(dest => dest.RuleCriterias, opt => opt.MapFrom(src => src.AlertRulesCriteria))
                .ForMember(dest => dest.RuleDevices, opt => opt.MapFrom(src => src.Devices));

            CreateMap<DeviceAlertRule, AlertRulesDTO>()
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.AlertRuleId, opt => opt.MapFrom(src => src.DeviceAlertRuleId))
                .ForMember(dest => dest.Action, opt => opt.MapFrom(src => src.DeviceAlertRuleActionId))
                .ForMember(dest => dest.Enabled, opt => opt.MapFrom(src => src.Enabled))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => src.CreatedOn))
                .ForMember(dest => dest.UpdatedOn, opt => opt.MapFrom(src => src.UpdatedOn))
                .ForMember(dest => dest.MatchedDevices, opt => opt.Ignore())
                .ForMember(dest => dest.ActionName,
                    opt => opt.MapFrom(src => src.EscalationChain != null ? src.EscalationChain.Name : "Auto Acknowledge"))
                .ForMember(dest => dest.CreatedByName,
                    opt => opt.MapFrom(src => $"Created By: {src.Creator.FullName} <{src.Creator.Email}>"))
                .ForMember(dest => dest.UpdatedByName,
                    opt => opt.MapFrom(src => src.Updater != null ? $"Last Edited By: {src.Updater.FullName} <{src.Updater.Email}>" : string.Empty))
                .ForMember(dest => dest.AlertRulesCriteria, opt => opt.MapFrom(src => src.RuleCriterias))
                .ForMember(dest => dest.Devices, opt => opt.MapFrom(src => src.RuleDevices));

            CreateMap<DeviceAlertType, DeviceAlertTypeDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => (DeviceAlertTypeEnumDTO)src.TypeId))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Title));

            CreateMap<AlertRuleActionDTO, DeviceAlertRuleAction>()
                .ForMember(dest => dest.DeviceAlertRuleActionId, opt => opt.MapFrom(src => src.DeviceAlertRuleActionId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (DeviceAlertRuleActionEnum)src.Value));

            CreateMap<DeviceAlertRuleAction, AlertRuleActionDTO>()
                .ForMember(dest => dest.DeviceAlertRuleActionId, opt => opt.MapFrom(src => src.DeviceAlertRuleActionId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (AlertRuleActionEnumDTO)src.Value));

            CreateMap<AlertRuleComparerDTO, DeviceAlertRuleComparer>()
                .ForMember(dest => dest.DeviceAlertRuleComparerId, opt => opt.MapFrom(src => src.DeviceAlertRuleComparerId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (AlertRuleActionEnumDTO)src.Value));

            CreateMap<DeviceAlertRuleComparer, AlertRuleComparerDTO>()
                .ForMember(dest => dest.DeviceAlertRuleComparerId, opt => opt.MapFrom(src => src.DeviceAlertRuleComparerId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (AlertRuleActionEnumDTO)src.Value));

            CreateMap<AlertThresholdLevelDTO, DeviceAlertThresholdLevel>()
                .ForMember(dest => dest.DeviceAlertThresholdLevelId, opt => opt.MapFrom(src => src.DeviceAlertThresholdLevelId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (AlertRuleActionEnumDTO)src.Value));

            CreateMap<DeviceAlertThresholdLevel, AlertThresholdLevelDTO>()
                .ForMember(dest => dest.DeviceAlertThresholdLevelId, opt => opt.MapFrom(src => src.DeviceAlertThresholdLevelId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => (AlertRuleActionEnumDTO)src.Value));


            CreateMap<DeviceAlertRulesDevices, AffectedDeviceDTO>()
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.ItemId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ItemId))
                .ForMember(dest => dest.IncludeSubfolders, opt => opt.MapFrom(src => src.IncludeSubfolders));
        }
    }
}