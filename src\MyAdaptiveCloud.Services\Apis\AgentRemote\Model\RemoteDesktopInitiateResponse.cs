namespace MyAdaptiveCloud.Services.Apis.AgentRemote.Model
{
    public class RemoteDesktopInitiateResponse
    {
        public string PublicSessionId { get; set; }
        public string SessionPrivateAgentInformationEncryptedBase64 { get; set; }
        /// <summary>
        /// This is the encrypted base64 encoded public client agent information for use in the browser.
        /// </summary>
        /// <remarks>
        /// This is encrypted with the public key of the session and is used to pass the agentUuId and privateSessionId through the browser back to the relay
        /// when initiating the client websocket connection.
        /// It is encrypted so that parties in the middle can NOT decrypt it or tamper with it as it is used as a one time session level authentication
        /// on the browser client side.
        /// </remarks>
        public string SessionPublicClientAgentInformationEncryptedBase64 { get; set; }
    }
}