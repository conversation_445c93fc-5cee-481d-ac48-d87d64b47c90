﻿using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using Dto = MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using MyAdaptiveCloud.Services.Requests.Connectwise;

namespace MyAdaptiveCloud.Services.Apis.ConnectWise
{
    public interface IConnectWiseApi
    {

        Task<List<Company>> GetCompaniesByName(string keyword);

        Task<Company> GetCompany(int companyId);

        Task CreateCompany(Company company);

        Task<CompanyType> GetCompanyType(string typeName);

        Task<List<Agreement>> GetAgreements(int companyId, string status = null);

        Task<Agreement> GetAgreement(int agreementId);

        Task<List<Contact>> GetContactsByEmail(string emailAddress, int? companyId = null);

        Task CreateContact(Contact contact);

        Task<List<Company>> GetCompaniesByIdentifier(string identifier);

        Task<T> GetCompanyByIdentifier<T>(string identifier) where T : new();

        Task<Agreement> CreateAgreement(CreateAgreement agreement);

        Task<AgreementTypeRef> GetAgreementType(string agreementTypeName);

        Task<BillingCycleRef> GetBillingCycleType(string billingCycleName);

        Task<List<BillingStatusRef>> GetClosedBillingStatuses();

        Task<Agreement> GetAgreementByNameAndType(int companyId, string name, string agreementType);

        Task<List<Addition>> GetAgreementProducts(int agreementId, List<string> products);

        Task<List<Contact>> GetCompaniesByEmailDomain(string emailAddress);

        Task<Agreement> PatchAgreement(int agreementId, Dictionary<string, object> agreementDict);

        Task<List<Invoice>> GetInvoices(int companyId, List<BillingStatusRef> billingStatuses = null, DateTime? fromDate = null);

        Task<Invoice> GetInvoice(int invoiceId);

        Task<HttpResponseMessage> GetInvoicePdf(int invoiceId);

        Task<List<ServiceTicket>> GetServiceTickets(string ticketName);

        Task<List<ServiceTicketConfiguration>> GetServiceTicketConfigurations(int ticketId);

        Task<List<Dto.ConfigurationModel>> GetConfigurations(ConnectWiseConfigurationRequest request);

        Task<List<Dto.ConfigurationModel>> GetConfigurationsByIds(List<int> configurationIds);

        Task<List<Configuration>> GetConfigurationsByName(string configurationName);

        Task<List<Company>> GetCompaniesByIdentifierOrName(string searchTerm);

        Task<Contact> GetContactById(int contactId);

        Task<List<CompanySite>> GetCompanyPrimarySites(int companyId);
    }
}
