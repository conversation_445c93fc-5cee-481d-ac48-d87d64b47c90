﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisAccessPolicy
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        [JsonPropertyName("version")]
        public int Version { get; set; }
        [Json<PERSON>ropertyName("trustee_id")]
        public string TrusteeId { get; set; }
        [JsonPropertyName("trustee_type")]
        public string TrusteeType { get; set; }
        [JsonPropertyName("issuer_id")]
        public string IssuerId { get; set; }
        [JsonPropertyName("tenant_id")]
        public string TenantId { get; set; }
        [JsonPropertyName("role_id")]
        public string RoleId { get; set; }
        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }
        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }
        [JsonPropertyName("deleted_at")]
        public string DeletedAt { get; set; }
        [JsonPropertyName("resource_namespace")]
        public string ResourceNamespace { get; set; }
    }
}
