-- liquibase formatted sql

-- changeset frodriguez:41a37458-ee2d-4a98-b2f9-06bf7cf0a1be context:main
CREATE OR REPLACE TABLE `organization_device_alert_threshold` (
	`organization_device_alert_threshold_id` INT(11) NOT NULL AUTO_INCREMENT,
	`organization_id` INT(11) NOT NULL,
	`device_alert_type_id` INT(11) NOT NULL,
	`device_alert_threshold_type_id` INT(11) NULL DEFAULT NULL,
	`device_alert_threshold_inheritance_type_id` INT(11) NULL DEFAULT NULL,
	`alert_threshold_warning` INT(11) NULL,
	`alert_threshold_error` INT(11) NULL,
	`alert_threshold_critical` INT(11) NULL,
	`trigger_after_minutes` INT(11) NULL DEFAULT NULL,
	`clear_after_minutes` INT(11) NULL DEFAULT NULL,
	`created_by` INT(11) NOT NULL,
	`created_on` DATETIME NOT NULL,
	`updated_by` INT(11) NULL DEFAULT NULL,
	`updated_on` DATETIME NULL DEFAULT NULL,
	<PERSON><PERSON>ARY KEY (`organization_device_alert_threshold_id`) USING BTREE,
	INDEX `fk_org_dv_al_th_organization_id_organization_organizationid` (`organization_id`) USING BTREE,
	INDEX `fk_org_dv_al_th_dv_alert_type_id_dv_alert_type_dev_al_type_id` (`device_alert_type_id`) USING BTREE,
	INDEX `fk_org_dv_al_th_dv_al_th_type_id_dv_al_th_type_dev_al_th_type_id` (`device_alert_threshold_type_id`) USING BTREE,
	INDEX `fk_org_dv_al_th_dv_al_th_inh_tid_dv_al_th_inh_t_dv_al_th_inh_tid` (`device_alert_threshold_inheritance_type_id`) USING BTREE,
	INDEX `fk_org_dv_al_th_created_by_user_userId` (`created_by`) USING BTREE,
	INDEX `fk_org_dv_al_th_updated_by_user_userId` (`updated_by`) USING BTREE,
	CONSTRAINT `fk_org_dv_al_th_created_by_user_userId` FOREIGN KEY (`created_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `fk_org_dv_al_th_dv_al_th_inh_tid_dv_al_th_inh_t_dv_al_th_inh_tid` FOREIGN KEY (`device_alert_threshold_inheritance_type_id`) REFERENCES `device_alert_threshold_inheritance_type` (`device_alert_threshold_inheritance_type_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `fk_org_dv_al_th_dv_al_th_type_id_dv_al_th_type_dev_al_th_type_id` FOREIGN KEY (`device_alert_threshold_type_id`) REFERENCES `device_alert_threshold_type` (`device_alert_threshold_type_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `fk_org_dv_al_th_dv_alert_type_id_dv_alert_type_dev_al_type_id` FOREIGN KEY (`device_alert_type_id`) REFERENCES `device_alert_type` (`device_alert_type_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,		
	CONSTRAINT `fk_org_dv_al_th_folder_id_device_folder_folderid` FOREIGN KEY (`organization_id`) REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `fk_org_dv_al_th_updated_by_user_userId` FOREIGN KEY (`updated_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- changeset frodriguez:21c8c427-de22-4fc0-9d35-f319c38de1c4 context:main
-- Insert default device alert threshold values for root organizations
SELECT UserId INTO @userId FROM `User` WHERE Email = '<EMAIL>';
SELECT device_alert_type_id INTO @cpuAlertTypeId FROM device_alert_type where name = 'CPU';
SELECT device_alert_type_id INTO @diskUsageAlertTypeId FROM device_alert_type where name = 'DiskUsage';
SELECT device_alert_type_id INTO @ramAlertTypeId FROM device_alert_type where name = 'RAM';
SELECT device_alert_type_id INTO @heartBeatAlertTypeId FROM device_alert_type where name = 'Heartbeat';
SELECT device_alert_threshold_type_id INTO @deviceAlertThresholdTypeId FROM device_alert_threshold_type where name = '%';
SELECT device_alert_threshold_inheritance_type_id INTO @inheritanceId FROM device_alert_threshold_inheritance_type where name = 'Override';


INSERT INTO organization_device_alert_threshold
(organization_id, device_alert_type_id, device_alert_threshold_type_id, 
device_alert_threshold_inheritance_type_id, alert_threshold_warning, alert_threshold_error, alert_threshold_critical, 
trigger_after_minutes, clear_after_minutes, 
created_by, created_on, updated_by, updated_on)
select organizationId, device_alert_type_id, device_alert_threshold_type_id, 
device_alert_threshold_inheritance_type_id, alert_threshold_warning, alert_threshold_error, alert_threshold_critical, 
trigger_after_minutes, clear_after_minutes, 
@userId as created_by, UTC_TIMESTAMP() as created_on, null as updated_by, null as updated_on 
from (
	SELECT @cpuAlertTypeId as device_alert_type_id, @deviceAlertThresholdTypeId as device_alert_threshold_type_id, 
	@inheritanceId as device_alert_threshold_inheritance_type_id, 
	75 as alert_threshold_warning, 85 as alert_threshold_error, 90 as alert_threshold_critical, 
	900 as trigger_after_minutes, 1 as clear_after_minutes
	UNION ALL 
	SELECT @diskUsageAlertTypeId, @deviceAlertThresholdTypeId, @inheritanceId, 75, 85, 90, 900, 1
	UNION ALL 
	SELECT @ramAlertTypeId, @deviceAlertThresholdTypeId, @inheritanceId, 75, 85, 90, 900, 1
	UNION ALL 
	SELECT @heartBeatAlertTypeId, @deviceAlertThresholdTypeId, @inheritanceId, 600, 1200, 1800, 900, 1
) a, Organization o 
where ParentOrganizationId is null