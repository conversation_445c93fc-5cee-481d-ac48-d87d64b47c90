﻿namespace MyAdaptiveCloud.Services.Apis.DDoSMitigation.Models
{
    public class Bgp
    {
        public string Id { get; set; }
        public string NeighborIp { get; set; }
        public int NeighborRemoteAs { get; set; }
        public string Prefix { get; set; }
        public string NextHop { get; set; }
        public List<int> AsPath { get; set; }
        public List<List<int>> Communities { get; set; }
        public int? Med { get; set; }
        public int LocalPref { get; set; }
        public int Origin { get; set; }
        public object Aggregator { get; set; }
        public bool AtomicAggregate { get; set; }
        public List<string> ExtendedCommunities { get; set; }
        public List<string> LargeCommunities { get; set; }
        public List<string> ClusterList { get; set; }
        public string OriginatorId { get; set; }
        public bool Simulate { get; set; }
    }
}