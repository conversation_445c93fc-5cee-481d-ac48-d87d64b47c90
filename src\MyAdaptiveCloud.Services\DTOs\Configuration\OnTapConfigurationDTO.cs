using System.Text;

namespace MyAdaptiveCloud.Services.DTOs.Configuration;

public class OnTapConfigurationDTO
{
    public const string Category = "Ontap Api";

    public string BaseUrl { get; set; }
    public string User { get; set; }
    public string Password { get; set; }
    public string AggregateName { get; set; }
    public string BasePortName { get; set; }
    public string DisableSslChecks { get; set; }
    public string AuthHeader => Convert.ToBase64String(Encoding.UTF8.GetBytes($"{User}:{Password}"));
}