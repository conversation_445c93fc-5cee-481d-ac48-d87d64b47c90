﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.OrganizationMapping;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.OrganizationMappings;

namespace MyAdaptiveCloud.Services.AutoMapper.OrganizationMapping
{
    public class OrganizationMappingMapperProfile : Profile
    {
        public OrganizationMappingMapperProfile()
        {
            CreateMap<ConnectWiseOrganizationMapping, OrganizationMappingDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PrimaryId, opt => opt.MapFrom(src => src.CompanyId.ToString()))
                .ForMember(dest => dest.PrimaryName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.SecondaryId, opt => opt.Ignore())
                .ForMember(dest => dest.SecondaryName, opt => opt.Ignore())
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => Applications.ConnectWise.ToString()));
            CreateMap<BillingDBOrganizationMapping, OrganizationMappingDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PrimaryId, opt => opt.MapFrom(src => src.CompanyId.ToString()))
                .ForMember(dest => dest.PrimaryName, opt => opt.MapFrom(src => src.CompanyShortName))
                .ForMember(dest => dest.SecondaryId, opt => opt.Ignore())
                .ForMember(dest => dest.SecondaryName, opt => opt.Ignore())
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => Applications.BillingDB.ToString()));
            CreateMap<CloudInfraOrganizationMapping, OrganizationMappingDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PrimaryId, opt => opt.MapFrom(src => src.AccountId.ToString()))
                .ForMember(dest => dest.PrimaryName, opt => opt.MapFrom(src => src.AccountName))
                .ForMember(dest => dest.SecondaryId, opt => opt.MapFrom(src => src.DomainName))
                .ForMember(dest => dest.SecondaryName, opt => opt.MapFrom(src => src.DomainId))
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => Applications.CloudInfra.ToString()));
            CreateMap<DataProtectionOrganizationMapping, OrganizationMappingDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PrimaryId, opt => opt.MapFrom(src => src.PrimaryId))
                .ForMember(dest => dest.PrimaryName, opt => opt.MapFrom(src => src.PrimaryName))
                .ForMember(dest => dest.SecondaryId, opt => opt.MapFrom(src => src.SecondaryId))
                .ForMember(dest => dest.SecondaryName, opt => opt.MapFrom(src => src.SecondaryName))
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => Applications.DataProtection.ToString()));
            CreateMap<TenaxOrganizationMapping, OrganizationMappingDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PrimaryId, opt => opt.MapFrom(src => src.MSPId))
                .ForMember(dest => dest.PrimaryName, opt => opt.MapFrom(src => src.MSPName))
                .ForMember(dest => dest.SecondaryId, opt => opt.MapFrom(src => src.CustomerTenantId))
                .ForMember(dest => dest.SecondaryName, opt => opt.MapFrom(src => src.CustomerName))
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => Applications.Tenax.ToString()));
        }
    }
}