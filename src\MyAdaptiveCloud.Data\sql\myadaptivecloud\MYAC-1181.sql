-- liquibase formatted sql

-- changeset frodriguez:edee60ee-9389-4089-add7-cef833ef1190 context:main
CREATE TABLE IF NOT EXISTS `device_alert_downtime_to_organization` (    
	`device_alert_downtime_to_organization_id` INT NOT NULL AUTO_INCREMENT,
    `device_alert_downtime_id` INT NOT NULL,   
    `organization_id` INT NOT NULL,
	PRIMARY KEY (`device_alert_downtime_to_organization_id`),
	UNIQUE INDEX `ux_device_alert_downtime_id_organization_id` (`device_alert_downtime_id`, `organization_id`) USING BTREE,
    CONSTRAINT `fk_device_alert_downtime_organization_id` FOR<PERSON><PERSON><PERSON> KEY (`organization_id`) REFERENCES `Organization` (`OrganizationId`),
    CONSTRAINT `fk_device_alert_downtime_organization_device_alert_downtime` FOREIGN KEY (`device_alert_downtime_id`) REFERENCES `device_alert_downtime` (`device_alert_downtime_id`)
);
