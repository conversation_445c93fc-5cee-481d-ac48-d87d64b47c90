﻿using AutoMapper;
using MyAdaptiveCloud.Data.Agent;
using MyAdaptiveCloud.Services.DTOs.Agent.AgentManagement;
using MyAdaptiveCloud.Services.Requests.AgentManagement.MsiManagement;

namespace MyAdaptiveCloud.Services.AutoMapper.Agents
{
    public class ReleaseTagProfile : Profile
    {
        public ReleaseTagProfile()
        {
            CreateMap<ReleaseTag, ReleaseTagDTO>();

            CreateMap<ReleaseTag, ReleaseTagIdDTO>()
                .ForMember(dest => dest.AgentVersion, opt => opt.MapFrom(src =>
                    src.AgentService != null
                        ? $"v{src.AgentService.Major}.{src.AgentService.Minor}.{src.AgentService.Build}"
                        : null));

            CreateMap<Service, ServiceVersionDTO>()
                .ForMember(dest => dest.Version, opt => opt.MapFrom(src => $"{src.Major}.{src.Minor}.{src.Build}"))
                .ForMember(dest => dest.ReleaseDate, opt => opt.MapFrom(src => src.Ts.UtcDateTime))
                .ForMember(dest => dest.ServiceId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

            CreateMap<Service, ServiceVersionListDTO>()
                .ForMember(dest => dest.Version, opt => opt.MapFrom(src => $"{src.Major}.{src.Minor}.{src.Build}"))
                .ForMember(dest => dest.ReleaseDate, opt => opt.MapFrom(src => src.Ts.UtcDateTime))
                .ForMember(dest => dest.ServiceId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore())
                .ForMember(dest => dest.CanMarkObsolete, opt => opt.Ignore())
                .ForMember(dest => dest.AgentCount, opt => opt.Ignore())
                .ForMember(dest => dest.ServiceType, opt => opt.Ignore())
                .ForMember(dest => dest.ReleaseTag, opt => opt.Ignore());

            CreateMap<UploadInstallerRequest, UploadInstallerDTO>()
                .ForMember(dest => dest.ServiceName, opt =>
                    opt.MapFrom((src, dest, destMember, context) =>
                        context.Items.ContainsKey("ServiceName")
                            ? context.Items["ServiceName"]?.ToString()
                            : null))
                .ForMember(dest => dest.DefaultInstallerFilePath, opt => opt.Ignore())
                .ForMember(dest => dest.OriginalFileName, opt => opt.Ignore())
                .ForMember(dest => dest.Platform, opt => opt.Ignore())
                .ForMember(dest => dest.FilePath, opt => opt.Ignore());
        }
    }
}