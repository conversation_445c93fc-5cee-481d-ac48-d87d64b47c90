﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisOfferingItem
    {
        [JsonPropertyName("application_id")]
        public string ApplicationId { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("edition")]
        public string Edition { get; set; }

        [JsonPropertyName("usage_name")]
        public string UsageName { get; set; }

        [JsonPropertyName("tenant_id")]
        public string TenantId { get; set; }

        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("deleted_at")]
        public string DeletedAt { get; set; }

        [JsonPropertyName("status")]
        public int Status { get; set; }
#nullable enable
        [JsonPropertyName("locked")]
        public bool? Locked { get; set; }
#nullable disable
        [Json<PERSON>ropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("measurement_unit")]
        public string MeasurementUnit { get; set; }

        [Json<PERSON>ropertyName("infra_id")]
        public string InfraId { get; set; }

        [JsonPropertyName("quota")]
        public AcronisOfferingItemQuota Quota { get; set; }
    }
}