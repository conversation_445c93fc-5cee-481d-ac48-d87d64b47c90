﻿using MyAdaptiveCloud.Services.Apis.CloudStack.Model;
using MyAdaptiveCloud.Services.Apis.CloudStack.Requests;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack
{
    public interface ICloudStackApi
    {
        Task<List<AccountResponse>> GetAccountsForDomainId(Guid domainId);

        Task<Domain> GetRootDomain();

        Task<List<AccountResponse>> GetAccounts();

        Task<List<AccountResponse>> GetAllAccounts();

        Task<AccountResponse> GetAccountById(Guid accountId);

        Task<Domain> GetDomainById(Guid domainId);

        Task<List<Domain>> GetDomains(DomainRequest domainRequest);

        Task<List<Domain>> GetDomainChildren(Guid? domainId = null);

        Task<(int, List<VirtualMachine>)> GetVirtualMachines(Guid? domainId = null, int? pageSize = null, int? pageNumber = null);

        Task<(int, List<VirtualMachine>)> GetVirtualMachinesByDomainId(Guid domainId);

        Task<List<OsCategory>> GetOsCategories();

        Task<List<OsType>> GetOsTypes(Guid? osCategoryId = null);

        Task<Domain> CreateDomain(Domain domain);

        Task<DeleteResponse> RemoveDomain(Domain domain, bool cleanup = true);

        Task<List<User>> GetUsersByUsername(string username);
        Task<AccountResponse> CreateAccount(CreateAccountRequest account);
        Task<Idp> GetIdp();
        Task<SamlAuthorization> GetSamlAuthorization(Guid userId);
        Task AuthorizeSamlSso(Guid userId, bool authorized, Idp idp);
        Task<HttpResponseMessage> UpdateResourceLimit(Guid domainId, string accountName, Enum resourceType, int limit);
        Task<List<ResourceLimit>> GetResourceLimitsByDomainId(Guid domainId);
        Task<List<ResourceLimit>> GetResourceLimitsByAccountName(Guid domainId, string accountName);
        Task<HttpResponseMessage> UpdateAccountName(Guid accountId, string newAccountName);
        Task<HttpResponseMessage> DeleteUser(string userId);
        Task<UserResponse> CreateUser(CreateUser user);
        Task<HttpResponseMessage> MoveUser(string userId, string account, string accountId);
        Task<PublicIpAddress> GetPublicIPAddress(Guid domainId, string publicIPAddress);
        Task<List<User>> GetUsersByDomain(string domainId);
        Task<List<User>> GetUsersByAccount(string accountId, string domainId);
        Task<List<User>> GetUsersByEmail(string email);

        Task<UserKeys> GetUserKeys(Guid userId);

        Task<UserKeys> RegisterUserKeys(Guid userId);

        Task<User> GetUser(string userApiKey);

        Task<List<RolePermission>> GetRolePermissions(Guid roleId);

        Task<List<Network>> GetNetworks();
        Task<List<IpReservation>> GetIpReservations(Guid networkId);
        Task<IpReservation> GenerateIpReservation(string networkId);
        Task<bool> RemoveIpReservation(string id);
        Task<List<Network>> GetNetworksByAccount(string accountId);
        Task<List<Network>> GetNetworksByDomain(string domainId);
        Task<List<Network>> GetNetworksByDomainAndAccount(string domainId, string accountName);
        Task<Network> GetNetwork(Guid id);
        Task<IpReservation> CreateIpReservation(string networkId, string startIp, string endIp);
        Task<List<Configuration>> GetGlobalConfigurationSettings(string configurationName);
    }
}