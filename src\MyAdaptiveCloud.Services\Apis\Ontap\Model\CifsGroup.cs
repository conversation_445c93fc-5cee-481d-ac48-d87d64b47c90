namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class CifsGroup: OntapObject
{
    public string Description { get; set; }
    public string Sid { get; set; }
    public List<CifsGroupMember> Members { get; set; }
    public Svm Svm { get; set; }
    
    public CifsGroup() {}

    public CifsGroup(string name, string description, Guid svm, string svmName)
    {
        Name = name;
        Description = description;
        Svm = new Svm(svm);
        Svm.Name = svmName;
    }

    public CifsGroup(string name, string description)
    {
        Name = name;
        Description = description;
    }

    public override string ToString()
    {
        var members = String.Join(", ", Members?.Select(m => m.Name) ?? new List<string>());
        return $"Group: {Name} Sid: {Sid} Members: {members}";
    }
}

public class CifsGroupMember
{
    public string Name { get; set; }
    
    public CifsGroupMember() {}

    public CifsGroupMember(string name)
    {
        Name = name;
    }

    public override string ToString()
    {
        return $"Group Member: {Name}";
    }
}

public class CifsGroupMemberAdd
{
    public List<CifsGroupMember> Records { get; set; }

    public CifsGroupMemberAdd() {}

    public CifsGroupMemberAdd(List<string> newMembers)
    {
        Records = newMembers.Select(m => new CifsGroupMember(m)).ToList();
    }
}