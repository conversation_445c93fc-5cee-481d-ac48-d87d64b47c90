using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class ResourceLimit
    {
        public Guid DomainId { get; set; }
        public string Domain { get; set; }
        public string ResourceType { get; set; }
        public string ResourceTypeName { get; set; }
        public int Max { get; set; }
    }

    public class ListResourceLimitsResponse : CloudStackListResponse<ResourceLimit>
    {
        [JsonPropertyName("resourceLimit")]
        public override List<ResourceLimit> Items { get; set; } = new List<ResourceLimit>();
    }
}