﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisTenant
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("version")]
        public int Version { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("customer_type")]
        public string CustomerType { get; set; }

        [JsonPropertyName("parent_id")]
        public string ParentId { get; set; }

        [JsonPropertyName("kind")]
        public string Kind { get; set; }

        [JsonPropertyName("contacts")]
        public List<AcronisContact> Contacts { get; set; }

        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; }

        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("deleted_at")]
        public string DeletedAt { get; set; }

        [JsonPropertyName("customer_id")]
        public string CustomerId { get; set; }

        [JsonPropertyName("brand_id")]
        public int BrandId { get; set; }

        [JsonPropertyName("brand_uuid")]
        public string BrandUuid { get; set; }

        [JsonPropertyName("internal_tag")]
        public string InternalTag { get; set; }

        [JsonPropertyName("language")]
        public string Language { get; set; }

        [JsonPropertyName("owner_id")]
        public string OwnerId { get; set; }

        [JsonPropertyName("has_children")]
        public bool HasChildren { get; set; }

        [JsonPropertyName("ancestral_access")]
        public bool AncestralAccess { get; set; }

        [JsonPropertyName("update_lock")]
        public AcronisUpdateLock UpdateLock { get; set; }

        [JsonPropertyName("mfa_status")]
        public string MfaStatus { get; set; }

        [JsonPropertyName("pricing_mode")]
        public string PricingMode { get; set; }
#nullable enable
        [JsonPropertyName("default_idp_id")]
        public string? DefaultIdpId { get; set; }

        [JsonPropertyName("contact")]
        public AcronisContact? Contact { get; set; }
#nullable disable
    }
}