namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public enum JobState
{
    queued, running, paused, success, failure
}

public class Job : OntapObject
{
    public JobState State { get; set; }
    public string Message { get; set; }

    public bool IsDone()
    {
        return State == JobState.failure || State == JobState.success;
    }

    public bool IsPaused()
    {
        return State == JobState.paused;
    }

    public bool WasSuccessful()
    {
        return State == JobState.success;
    }

    public bool IsFailed()
    {
        return State == JobState.failure;
    }

    public Job ThrowOnFail(string message = "")
    {
        if (IsFailed())
        {
            throw new OntapApiException($"Job Failed {message} : {this}");
        }

        return this;
    }

    public override string ToString()
    {
        return $"Job: {Uuid} State: {State} Message: {Message}";
    }
}