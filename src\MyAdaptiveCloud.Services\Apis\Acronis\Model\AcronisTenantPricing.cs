﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisTenantPricing
    {
        [JsonPropertyName("version")]
        public long Version { get; set; }

        [JsonPropertyName("mode")]
        public string Mode { get; set; }
#nullable enable
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }
#nullable disable
        [JsonPropertyName("production_start_date")]
        public string ProductionStartDate { get; set; }
    }
}