namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Port : OntapObject
{
    public PortType Type { get; set; }
    public bool Enabled { get; set; }
    public string State { get; set; }
    public Vlan Vlan { get; set; }
    public Node Node { get; set; }
    public BroadcastDomain BroadcastDomain { get; set; }

    public Port() { }

    public Port(Port basePort, int vlanTag, Guid broadcastDomain)
    {
        Type = PortType.Vlan;
        Vlan = new Vlan(basePort.Uuid.ToString(), vlanTag);
        Node = new Node(basePort.Node.Uuid.ToString());
        Enabled = true;
        BroadcastDomain = new BroadcastDomain(broadcastDomain);
    }

    public override string ToString()
    {
        var vlanTag = Vlan?.Tag ?? 0;
        var node = Node?.Name ?? "";
        return $"Port {Name} Uuid: {Uuid} Type: {Type} Enabled: {Enabled} State: {State} Vlan: {vlanTag} Node: {node}";
    }
}

public enum PortType
{
    Vlan,
    Physical,
    Lag
}

public class Vlan
{
    public int Tag { get; set; }
    public VlanBasePort BasePort { get; set; }
    
    public Vlan() { }

    public Vlan(string basePortUuid, int vlanTag)
    {
        Tag = vlanTag;
        BasePort = new VlanBasePort(basePortUuid);
    }
}

public class VlanBasePort : OntapObject
{
    public Node Node { get; set; }
    
    public VlanBasePort() {}

    public VlanBasePort(string uuid)
    {
        Uuid = Guid.Parse(uuid);
    }
}