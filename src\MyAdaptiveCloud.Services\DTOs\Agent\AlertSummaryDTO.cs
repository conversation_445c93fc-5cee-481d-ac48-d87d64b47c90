using MyAdaptiveCloud.Services.DTOs.AlertRules;
using MyAdaptiveCloud.Services.DTOs.DeviceThresholds;
using System.Diagnostics;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    [DebuggerDisplay("{Hostname}-{AgentId} {StartedOn} {AlertThresholdLevel} {DeviceAlertType}")]
    public class AlertSummaryDTO
    {
        public int AgentId { get; set; }

        public int DeviceAlertId { get; set; }

        public string Hostname { get; set; }

        public AlertThresholdLevelEnumDTO AlertThresholdLevel { get; set; }

        public DeviceAlertTypeEnumDTO DeviceAlertType { get; set; }

        public DateTimeOffset StartedOn { get; set; }

        public DateTimeOffset? AcknowledgedOn { get; set; }

        public string AcknowledgedBy { get; set; }

        public string ExceededValue { get; set; }

        public int DeviceAlertThresholdLevelId { get; set; }

        public int DeviceAlertTypeId { get; set; }

        public int? FolderDeviceAlertThresholdId { get; set; }

        public int? DeviceAlertThresholdOverrideId { get; set; }

        public int? OrganizationDeviceAlertThresholdId { get; set; }

        public bool DeviceInScheduleDowntime { get; set; }

        public DateTimeOffset? DeviceScheduleDowntimeEndDate { get; set; }
    }
}
