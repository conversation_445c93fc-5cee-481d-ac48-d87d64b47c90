using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Caching.Memory;
using System.Globalization;
using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using MyAdaptiveCloud.Services.Services;
using Dto = MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using Contact = MyAdaptiveCloud.Services.Apis.ConnectWise.Model.Contact;
using Company = MyAdaptiveCloud.Services.Apis.ConnectWise.Model.Company;
using MyAdaptiveCloud.Services.Requests.Connectwise;
using MyAdaptiveCloud.Services.DTOs.Configuration;

namespace MyAdaptiveCloud.Services.Apis.ConnectWise
{
    public class ConnectWiseApi : IConnectWiseApi
    {
        private readonly HttpClient _client;
        private readonly IMemoryCache _cache;
        private readonly IConfigurationService _configurationService;

        public ConnectWiseApi(HttpClient client, IConfigurationService configurationService, IMemoryCache cache)
        {
            _client = client;
            _cache = cache;
            _configurationService = configurationService;
            var cwConfig = _configurationService.GetConnectWiseConfiguration().GetAwaiter().GetResult();
            _client.BaseAddress = new Uri(cwConfig.BaseUrl);
            _client.DefaultRequestHeaders.Add("clientID", cwConfig.ClientId); /// ConnectWise ClientId that is required for all calls  
            var authenticationHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{cwConfig.Company}+{cwConfig.PublicKey}:{cwConfig.PrivateKey}"));
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authenticationHeader);
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public static string ToConnectWiseDateTime(DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-ddThh:mm:ss", CultureInfo.InvariantCulture);
        }

        public async Task<List<Company>> GetCompaniesByName(string name)
        {
            var requestParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(name))
            {
                requestParams.Add("conditions", $"deletedFlag=false AND name contains \"{name}%\"");
            }

            var response = await ExecuteGetRequest("/company/companies", requestParams);
            return await GetDeserializedResponse<List<Company>>(response);
        }

        public async Task<List<Company>> GetCompaniesByIdentifier(string identifier)
        {
            var requestParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(identifier))
            {
                // Removed deletedFlag=false because of MYAC-1189 error. We need to return all companies, even deleted
                requestParams.Add("conditions", $"identifier contains \"{identifier}%\"");
            }

            requestParams.Add("fields", "id,identifier,name");
            var response = await ExecuteGetRequest("/company/companies", requestParams);
            return await GetDeserializedResponse<List<Company>>(response);
        }

        public async Task<List<Company>> GetCompaniesByIdentifierOrName(string searchTerm)
        {
            var requestParams = new Dictionary<string, string>
            {
                {
                    "conditions",
                    $"deletedFlag=false and status/name != \"{ConnectWiseConfigurationDTO.DuplicateStatusName}\" and leadFlag=false and (identifier contains \"{searchTerm}%\" or name contains \"{searchTerm}%\")"
                },
                { "fields", "id,identifier,name,defaultContact/id" },
                { "orderBy", "name" }
            };
            var response = await ExecuteGetRequest("/company/companies", requestParams, pageSize: 25);
            return await GetDeserializedResponse<List<Company>>(response);
        }

        public async Task<T> GetCompanyByIdentifier<T>(string identifier) where T : new()
        {
            var requestParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(identifier))
            {
                requestParams.Add("conditions", $"identifier=\"{identifier}\"");
            }

            var response = await ExecuteGetRequest("/company/companies", requestParams);
            var companies = await GetDeserializedResponse<List<T>>(response);
            return companies.FirstOrDefault();
        }

        public async Task<Company> GetCompany(int companyId)
        {
            var response = await ExecuteGetRequest($"/company/companies/{companyId}", new Dictionary<string, string>());
            return await GetDeserializedResponse<Company>(response);
        }

        public async Task<List<CompanySite>> GetCompanyPrimarySites(int companyId)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "conditions", $"inactiveFlag=false and (primaryAddressFlag=true or defaultShippingFlag=true or defaultBillingFlag=true)" },
                { "fields", "name,addressLine1,city,zip,stateReference,country/name,primaryAddressFlag,defaultShippingFlag,defaultBillingFlag,billSeparateFlag,phoneNumber" }
            };
            var response = await ExecuteGetRequest($"/company/companies/{companyId}/sites", requestParams);
            return await GetDeserializedResponse<List<CompanySite>>(response);
        }

        public async Task CreateCompany(Company company)
        {
            await ExecutePostRequest("/company/companies", company);
        }

        public async Task<CompanyType> GetCompanyType(string typeName)
        {
            return await _cache.GetOrCreateAsync($"CompanyType.{typeName}", async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddDays(1);
                var requestParams = new Dictionary<string, string>
                {
                    { "conditions", $"name='{typeName}'" },
                };
                var response = await ExecuteGetRequest($"/company/companies/types", requestParams);
                var companyTypes = await GetDeserializedResponse<List<CompanyType>>(response);
                return companyTypes.FirstOrDefault();
            });
        }

        public async Task<List<Agreement>> GetAgreements(int companyId, string status = null)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "conditions", $"company/id={companyId}" },
            };
            if (!string.IsNullOrEmpty(status))
            {
                requestParams["conditions"] += $" and agreementStatus=\"{status}\"";
            }

            var response = await ExecuteGetRequest($"/finance/agreements", requestParams);
            return await GetDeserializedResponse<List<Agreement>>(response);
        }

        public async Task<Agreement> GetAgreementByNameAndType(int companyId, string name, string agreementType)
        {
            // TODO Figure out what to do here, as ConnectWise won't allow us to add an agreement with a duplicate name, but that agreement may not be active
            //var status = "Active";
            var agreementTypeRef = await GetAgreementType(agreementType);
            var requestParams = new Dictionary<string, string>
            {
                // TODO See above
                //{ "conditions", $"company/id={companyId} and agreementStatus=\"{status}\" and name=\"{name}\" and type/id={agreementTypeRef.Id}" },
                { "conditions", $"company/id={companyId} and name=\"{name}\" and type/id={agreementTypeRef.Id}" },
            };

            var response = await ExecuteGetRequest($"/finance/agreements", requestParams);
            return (await GetDeserializedResponse<List<Agreement>>(response)).FirstOrDefault();
        }

        public async Task<Agreement> GetAgreement(int agreementId)
        {
            var response = await ExecuteGetRequest($"/finance/agreements/{agreementId}", new Dictionary<string, string>());
            return await GetDeserializedResponse<Agreement>(response);
        }

        // This needs the following elements to POST successfully:
        // Name - should be AdaptiveCloud
        // StartDate - should be now
        // Type - Ref containing the Agreement Type for 'Adaptive Cloud', should be configured in ConfigurationValues and then looked up for the proper Id
        // Company - CompanyRef containing either the company Id or the identifier or both
        // Contact - ContactRef containing the person's contact Id
        // BillingCycle - Ref containing the Billing Cycle type for 'Monthly', should be configured in ConfigurationValues and then looked up for the proper Id
        // NoEndingDateFlag - Should be True, as we don't want to pre-set an ending date to the agreement
        // The rest can be left null
        // The response does contain the JSON for this Agreement, so it might be good to capture it as we need to map to this new agreement id
        public async Task<Agreement> CreateAgreement(CreateAgreement agreement)
        {
            var response = await ExecutePostRequest("/finance/agreements", agreement);
            return await GetDeserializedResponse<Agreement>(response);
        }

        public async Task<Agreement> PatchAgreement(int agreementId, Dictionary<string, object> agreementDict)
        {
            var reqDict = new List<Dictionary<string, object>>();
            foreach (KeyValuePair<string, object> kvp in agreementDict)
            {
                reqDict.Add(new Dictionary<string, object>()
                {
                    { "op", "replace" },
                    { "path", kvp.Key },
                    { "value", kvp.Value },
                });
            }

            var response = await ExecutePatchRequest($"/finance/agreements/{agreementId}", reqDict);
            return await GetDeserializedResponse<Agreement>(response);
        }

        // Checks an agreement for any of a given list of products. Returns true if it finds any of the products
        public async Task<List<Addition>> GetAgreementProducts(int agreementId, List<string> products)
        {
            var productQueries = new List<string>();
            foreach (var product in products)
            {
                productQueries.Add($"product/identifier=\"{product}\"");
            }

            var requestParams = new Dictionary<string, string>()
            {
                { "conditions", string.Join(" or ", productQueries) },
            };

            var response = await ExecuteGetRequest($"/finance/agreements/{agreementId}/additions", requestParams);
            return await GetDeserializedResponse<List<Addition>>(response);
        }

        public async Task<List<Contact>> GetContactsByEmail(string emailAddress, int? companyId = null)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "childconditions", $"communicationItems/value='{emailAddress}' AND communicationItems/communicationType='Email'" },
            };
            if (companyId != null)
            {
                requestParams["conditions"] = $"company/id={companyId}";
            }

            var request = "/company/contacts";
            var response = await ExecuteGetRequest(request, requestParams);
            var result = await GetDeserializedResponse<List<Contact>>(response);
            return result.Where(contact => !contact.InactiveFlag && contact.Company != null).ToList();
        }

        public async Task<Contact> GetContactById(int contactId)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "fields", "firstName,lastName,communicationItems/value,communicationItems/communicationType,company/id" }
            };
            var request = $"/company/contacts/{contactId}";
            var response = await ExecuteGetRequest(request, requestParams);
            return await GetDeserializedResponse<Contact>(response);
        }

        public async Task<List<Contact>> GetCompaniesByEmailDomain(string emailAddress)
        {
            var cwConfig = await _configurationService.GetConnectWiseConfiguration();

            var contacts = new List<Contact>();
            var emailDomain = emailAddress.Substring(emailAddress.IndexOf('@') + 1);
            // Only search CW if the email domain isn't a common one
            if (!cwConfig.CommonEmailDomains.Contains(emailDomain))
            {
                var requestParams = new Dictionary<string, string>
                {
                    { "conditions", "inactiveFlag=false" },
                    { "childconditions", $"communicationItems/domain='@{emailDomain}' AND communicationItems/communicationType='Email'" },
                };
                var request = "/company/contacts";
                var response = await ExecuteGetRequest(request, requestParams);
                contacts = await GetDeserializedResponse<List<Contact>>(response);
            }

            return contacts.Where(contact => contact.Company != null).OrderBy(contact => contact.Company.Name).DistinctBy(contact => contact.Company.Id).ToList();
        }

        public async Task CreateContact(Contact contact)
        {
            await ExecutePostRequest("/company/contacts", contact);
        }

        public async Task<AgreementTypeRef> GetAgreementType(string agreementTypeName)
        {
            return await _cache.GetOrCreateAsync($"AgrType.{agreementTypeName}", async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddDays(1);

                var requestParams = new Dictionary<string, string>
                {
                    { "conditions", $"name='{agreementTypeName}'" },
                };
                var response = await ExecuteGetRequest($"/finance/agreements/types", requestParams);
                var agreementTypes = await GetDeserializedResponse<List<AgreementTypeRef>>(response);
                return agreementTypes.FirstOrDefault();
            });
        }

        public async Task<BillingCycleRef> GetBillingCycleType(string billingCycleName)
        {
            return await _cache.GetOrCreateAsync($"BillCT.{billingCycleName}", async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddDays(1);

                var requestParams = new Dictionary<string, string>
                {
                    { "conditions", $"name='{billingCycleName}'" },
                };
                var response = await ExecuteGetRequest($"/finance/billingCycles", requestParams);
                var billingCycleTypes = await GetDeserializedResponse<List<BillingCycleRef>>(response);
                return billingCycleTypes.FirstOrDefault();
            });
        }

        public async Task<List<BillingStatusRef>> GetClosedBillingStatuses()
        {
            return await _cache.GetOrCreateAsync($"ClBillSt", async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddHours(1);

                var requestParams = new Dictionary<string, string>
                {
                    { "conditions", $"closedFlag=true" },
                };
                var response = await ExecuteGetRequest($"/finance/billingStatuses", requestParams);
                var closedBillingStatuses = await GetDeserializedResponse<List<BillingStatusRef>>(response);
                return closedBillingStatuses;
            });
        }

        public async Task<List<Invoice>> GetInvoices(int companyId, List<BillingStatusRef> billingStatuses = null, DateTime? fromDate = null)
        {
            var conditions = new List<string> { $"company/id={companyId}", };
            if (billingStatuses != null && billingStatuses.Count > 0)
            {
                var statusInClause = string.Join(",", billingStatuses.Select(status => status.Id.ToString()));
                conditions.Add($"status/id IN ({statusInClause})");
            }

            if (fromDate != null)
            {
                conditions.Add($"date>=[{ToConnectWiseDateTime(fromDate.Value)}]");
            }

            var requestParams = new Dictionary<string, string>
            {
                { "conditions", string.Join(" and ", conditions) },
            };
            var response = await ExecuteGetRequest($"/finance/invoices", requestParams);
            return await GetDeserializedResponse<List<Invoice>>(response);
        }

        public async Task<Invoice> GetInvoice(int invoiceId)
        {
            var requestParams = new Dictionary<string, string>();
            var response = await ExecuteGetRequest($"/finance/invoices/{invoiceId}", requestParams);
            return await GetDeserializedResponse<Invoice>(response);
        }

        public async Task<HttpResponseMessage> GetInvoicePdf(int invoiceId)
        {
            var requestParams = new Dictionary<string, string>();
            return await ExecuteGetRequest($"/finance/invoices/{invoiceId}/pdf", requestParams, HttpCompletionOption.ResponseHeadersRead);
        }

        public async Task<List<Dto.ConfigurationModel>> GetConfigurations(ConnectWiseConfigurationRequest request)
        {
            var requestParams = new Dictionary<string, string>();

            StringBuilder conditions = new StringBuilder("ActiveFlag = true");

            if (!string.IsNullOrEmpty(request.ConfigurationName) || !string.IsNullOrEmpty(request.ConfigurationType) || !string.IsNullOrEmpty(request.CompanyName))
            {
                conditions.Append(" AND ");
                bool hasPreviousCondition = false;
                if (!string.IsNullOrEmpty(request.ConfigurationName))
                {
                    conditions.Append($" Name contains \"{request.ConfigurationName}%\"");
                    hasPreviousCondition = true;
                }

                if (!string.IsNullOrEmpty(request.ConfigurationType))
                {
                    conditions.Append($"{(hasPreviousCondition ? " OR " : "")} Type/Name contains \"{request.ConfigurationType}%\"");
                    hasPreviousCondition = true;
                }

                if (!string.IsNullOrEmpty(request.CompanyName))
                {
                    conditions.Append($"{(hasPreviousCondition ? " OR " : "")} Company/Name contains \"{request.CompanyName}%\"");
                }
            }

            requestParams.Add("conditions", conditions.ToString());
            requestParams.Add("fields", "id,name,type/name,company/name");
            var response = await ExecuteGetRequest($"/company/configurations", requestParams);
            var deserializedResponse = await GetDeserializedResponse<List<Model.Configuration>>(response);
            return deserializedResponse.Select(x => new Dto.ConfigurationModel { Id = x.Id, Name = x.Name, Type = x.Type.Name, Company = x.Company.Name }).ToList();
        }

        public async Task<List<Dto.ConfigurationModel>> GetConfigurationsByIds(List<int> ids)
        {
            var requestParams = new Dictionary<string, string>();

            StringBuilder conditions = new StringBuilder("ActiveFlag = true");

            for (int i = 0; i < ids.Count; i++)
            {
                if (i == 0)
                {
                    conditions.Append(" AND ");
                }
                else
                {
                    conditions.Append(" OR ");
                }

                conditions.Append($"id = {ids[i]}");
            }

            requestParams.Add("conditions", conditions.ToString());
            requestParams.Add("fields", "id,name,type/name,company/name");
            var response = await ExecuteGetRequest($"/company/configurations", requestParams);
            var deserializedResponse = await GetDeserializedResponse<List<Model.Configuration>>(response);
            return deserializedResponse.Select(x => new Dto.ConfigurationModel { Id = x.Id, Name = x.Name, Type = x.Type.Name, Company = x.Company.Name }).ToList();
        }

        public async Task<List<ServiceTicket>> GetServiceTickets(string ticketName)
        {
            var requestParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(ticketName))
            {
                StringBuilder conditions = new StringBuilder($"summary LIKE \"{ticketName}%\"");
                int numericValue;
                bool isNumber = int.TryParse(ticketName, out numericValue);
                if (isNumber)
                {
                    conditions.Append($" OR id LIKE {numericValue}");
                }

                requestParams.Add("conditions", conditions.ToString());
            }

            requestParams.Add("fields", "id,summary,initialDescription");
            var response = await ExecuteGetRequest("/service/tickets", requestParams);
            return await GetDeserializedResponse<List<ServiceTicket>>(response);
        }

        public async Task<List<ServiceTicketConfiguration>> GetServiceTicketConfigurations(int ticketId)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "fields", "id,deviceIdentifier" }
            };
            var response = await ExecuteGetRequest($"/service/tickets/{ticketId}/configurations", requestParams);
            return await GetDeserializedResponse<List<ServiceTicketConfiguration>>(response);
        }

        public async Task<List<Configuration>> GetConfigurationsByName(string configurationName)
        {
            var requestParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(configurationName))
            {
                StringBuilder conditions = new StringBuilder("ActiveFlag = true");
                conditions.Append(" AND ");
                conditions.Append($" Name contains \"{configurationName}%\"");
                conditions.Append($" OR Type/Name contains \"{configurationName}%\"");
                conditions.Append($" OR Company/Name contains \"{configurationName}%\"");
                int numericValue;
                bool isNumber = int.TryParse(configurationName, out numericValue);
                if (isNumber)
                {
                    conditions.Append($" OR id LIKE {numericValue}");
                }

                requestParams.Add("conditions", conditions.ToString());
            }

            requestParams.Add("fields", "id,name");
            var response = await ExecuteGetRequest($"/company/configurations", requestParams);
            return await GetDeserializedResponse<List<Configuration>>(response);
        }

        public async Task UpdateCompanyIdentifier(int companyId, string companyIdentifier)
        {
            var patch = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>()
                {
                    { "op", "replace" },
                    { "path", "identifier" },
                    { "value", companyIdentifier },
                }
            };
            await ExecutePatchRequest($"/company/companies/{companyId}", patch);
        }

        // 04/25/22 BLG - added completionOption to control when HttpClient operation completes; after full response read (default) vs headers only
        private async Task<HttpResponseMessage> ExecuteGetRequest(string relativeUrlString, IDictionary<string, string> requestParams,
            HttpCompletionOption completionOption = HttpCompletionOption.ResponseContentRead, int pageSize = 1000)
        {
            requestParams.Add("pageSize", pageSize.ToString());
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = QueryHelpers.AddQueryString(_client.BaseAddress.AbsolutePath + relativeUrlString, requestParams);
            HttpResponseMessage response = await _client.GetAsync(relativeUriString, completionOption);
            return response;
        }

        private async Task<HttpResponseMessage> ExecutePostRequest<T>(string relativeUrlString, T body)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;
            var content = new StringContent(JsonSerializer.Serialize(body, new JsonSerializerOptions()
            {
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            }), Encoding.UTF8, "application/json");
            HttpResponseMessage response = await _client.PostAsync(relativeUriString, content);
            return response;
        }

        private async Task<HttpResponseMessage> ExecutePatchRequest<T>(string relativeUrlString, T body)
        {
            // Note that Uris will replace the AbsolutePath part when given a new relative Uri, so need to preserve this and add it to our relateive uri
            string relativeUriString = _client.BaseAddress.AbsolutePath + relativeUrlString;
            var content = new StringContent(JsonSerializer.Serialize(body, new JsonSerializerOptions()
            {
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            }), Encoding.UTF8, "application/json");
            HttpResponseMessage response = await _client.PatchAsync(relativeUriString, content);
            return response;
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            if (!response.IsSuccessStatusCode && response.StatusCode != System.Net.HttpStatusCode.NotFound)
            {
                throw new Exception(await response.Content?.ReadAsStringAsync());
            }

            T res = new T();

            if (response.Content == null || response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return res;
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };
            return await JsonSerializer.DeserializeAsync<T>(await response.Content.ReadAsStreamAsync(), options);
        }
    }
}