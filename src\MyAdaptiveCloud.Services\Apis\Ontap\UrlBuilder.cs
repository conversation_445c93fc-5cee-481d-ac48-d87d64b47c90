using Microsoft.AspNetCore.WebUtilities;
using System.Text.RegularExpressions;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class UrlBuilder
{
    private static readonly Regex RenderExpr = new(@"%([A-Za-z0-9_.\-]+)%", RegexOptions.Compiled);
    public string BaseUrl { get; set; }
    public Dictionary<string, string> PathParams { get; set; }
    public Dictionary<string, string> QueryParams { get; set; }

    public static UrlBuilder ResourceUrl(string baseUrl, Guid uuid)
    {
        return new UrlBuilder($"/{baseUrl.TrimStart('/')}/%uuid%")
            .AddPathParam("uuid", uuid.ToString());
    }

    public static UrlBuilder ListUrl(string baseUrl)
    {
        return new UrlBuilder(baseUrl).AddFields();
    }

    public static UrlBuilder ListUrl(string baseUrl, params string[] parts)
    {
        if (parts.Length % 2 != 0)
        {
            throw new ArgumentException("This requires an even number of arguments for key/value pairs");
        }

        var url = ListUrl(baseUrl);

        for (int i = 0; i < parts.Length; i += 2)
        {
            url.AddPathParam(parts[i], parts[i + 1]);
        }

        return url;
    }

    public UrlBuilder(string baseUrl)
    {
        BaseUrl = baseUrl;
        PathParams = new Dictionary<string, string>();
        QueryParams = new Dictionary<string, string>();
    }

    public UrlBuilder AddPathParam(string key, string value)
    {
        if (key == null || value == null)
        {
            throw new ArgumentException("Null values are not allowed");
        }

        PathParams.Add(key, value);

        return this;
    }

    public UrlBuilder AddPathParam(string key, Guid value)
    {
        PathParams.Add(key, value.ToString());

        return this;
    }

    public UrlBuilder AddPathParams(params string[] parts)
    {
        if (parts.Length % 2 != 0)
        {
            throw new ArgumentException("This requires an even number of arguments for key/value pairs");
        }

        for (int i = 0; i < parts.Length; i += 2)
        {
            PathParams.Add(parts[i], parts[i + 1]);
        }

        return this;
    }

    public UrlBuilder AddAllPathParams(IDictionary<string, string> pathParams)
    {
        PathParams = PathParams.Concat(pathParams).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        return this;
    }

    public UrlBuilder AddQueryParam(string key, string value)
    {
        QueryParams.Add(key, value);

        return this;
    }

    public UrlBuilder AddQueryParam(string key, Guid value)
    {
        QueryParams.Add(key, value.ToString());

        return this;
    }

    public UrlBuilder AddFields(string fields)
    {
        QueryParams.Add("fields", fields);

        return this;
    }

    public UrlBuilder AddFields()
    {
        QueryParams.Add("fields", "**");

        return this;
    }

    public UrlBuilder AddAllQueryParams(IDictionary<string, string> queryParams)
    {
        QueryParams = PathParams.Concat(queryParams).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        return this;
    }

    public string Render()
    {
        var newUrl = RenderExpr.Replace(BaseUrl, match =>
        {
            if (!PathParams.ContainsKey(match.Groups[1].Value))
            {
                throw new ArgumentException($"Path contains substitution without value: {match.Groups[1].Value}");
            }

            return Uri.EscapeDataString(PathParams[match.Groups[1].Value]);
        });

        return QueryHelpers.AddQueryString(newUrl, QueryParams);
    }

    public override string ToString()
    {
        var pathParams = string.Join(", ", PathParams.Select(kvp => $"{kvp.Key}:{kvp.Value}").ToList());
        var queryParams = string.Join(", ", QueryParams.Select(kvp => $"{kvp.Key}:{kvp.Value}").ToList());
        return $"URL: {BaseUrl} Path Params: {pathParams} Query Params: {queryParams} Rendered: {Render()}";
    }
}