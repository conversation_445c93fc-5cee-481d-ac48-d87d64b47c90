namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class SnapshotPolicy: OntapObject
{
    public bool Enabled { get; set; }
    public Svm Svm { get; set; }
    public List<SnapshotCopy> Copies { get; set; }
    
    public SnapshotPolicy() {}

    public SnapshotPolicy(string name, bool enabled, Guid svmId)
    {
        Name = name;
        Enabled = enabled;
        Svm = new Svm(svmId);
        Copies = new List<SnapshotCopy>();
    }

    public void  AddCopySchedule(int count, string prefix, string schedule, string snapmirrorLabel)
    {
        Copies.Add(new SnapshotCopy(schedule, prefix, count, snapmirrorLabel));
    }
}

public class SnapshotCopy
{
    public int Count { get; set; }
    public string Prefix { get; set; }
    public SnapshotSchedule Schedule { get; set; }
    public string SnapmirrorLabel { get; set; }
    
    public SnapshotCopy() {}

    public SnapshotCopy(string schedule, string prefix, int count, string snapmirrorLabel)
    {
        Schedule = new SnapshotSchedule(schedule);
        Prefix = prefix;
        Count = count;
        SnapmirrorLabel = snapmirrorLabel;
    }
}

public class SnapshotSchedule
{
    // Some normal defaults are monthly, weekly, daily, hourly
    public string Name { get; set; }
    
    public SnapshotSchedule() {}

    public SnapshotSchedule(string name)
    {
        Name = name;
    }
}