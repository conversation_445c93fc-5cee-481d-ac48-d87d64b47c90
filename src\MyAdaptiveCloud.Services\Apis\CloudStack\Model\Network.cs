using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model;

public class Network
{
    public Guid Id { get; set; }
    public string Account { get; set; }
    public string Domain { get; set; }
    public string DomainId { get; set; }
    public string Name { get; set; }
    public string DisplayText { get; set; }
    public string Vlan { get; set; }
    public string Gateway { get; set; }
    public string Cidr { get; set; }
    public string NetworkDomain { get; set; }
    public string BroadcastDomainType { get; set; }
    public Boolean CanUseForDeploy { get; set; }

    public int VlanInt()
    {
        return Int32.Parse(Vlan);
    }
}

public class ListNetworksResponse : CloudStackListResponse<Network>
{
    [JsonPropertyName("network")]
    public override List<Network> Items { get; set; } = new List<Network>();
}