﻿namespace MyAdaptiveCloud.Services.DTOs.Agent.RemoteDesktopConnection
{
    public class AgentRemoteDesktopConnectionStatus
    {
        public int RemoteDesktopConnectionId { get; set; }
        public AgentRemoteDesktopConnectionStatusEnumDTO RemoteDesktopConnectionStatusId { get; set; }
        public string RemoteDesktopConnectionUuId { get; set; }
        /// <summary>
        /// This gets set when RemoteDesktopConnectionStatusId is set to "ConnectedAgent"
        /// </summary>
        public string RemoteDesktopConnectionUrl { get; set; }
        public string RemoteDesktopConnectionUserFullName { get; set; }
    }
}