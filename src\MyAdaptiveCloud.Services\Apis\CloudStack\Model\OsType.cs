using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class OsType
    {
        public Guid Id { get; set; }
        public string Description { get; set; }
        public Guid OsCategoryId { get; set; }
        public bool isUserDefined { get; set; }
    }

    public class ListOsTypesResponse : CloudStackListResponse<OsType>
    {
        [JsonPropertyName("ostype")]
        public override List<OsType> Items { get; set; } = new List<OsType>();
    }
}