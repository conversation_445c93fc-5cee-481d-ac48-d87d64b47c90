-- liquibase formatted sql

-- changeset voviedo:3b623fc5-7110-4300-9a06-628a13dde505 --context:dev,test
INSERT INTO Organization (OrganizationId, Name, CreatedDate, IsActive, AllowSubOrg, <PERSON>ow<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>erified, ParentOrganizationId) 
    VALUES (9991, 'Test Report Organization', NOW(), 1, 1, 1, 0, 1, 0);

INSERT INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`, `Description`) 
VALUES (690, NULL, 9991, 'Folder 1', 'Folder 1');

INSERT INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`, `Description`) 
VALUES (691, NULL, 9991, 'Folder 2', 'Folder 2');

SELECT UserId INTO @PersonId FROM User WHERE Email = 'admin@localhost' LIMIT 1;
INSERT IGNORE INTO `User_Organization` (`OrganizationId`, `UserId`) VALUES (9991, @PersonId);
SELECT @UserOrganization := LAST_INSERT_ID();
INSERT IGNORE INTO `User_Organization_Mapping` (`RoleId`, `CreatedBy`, `CreatedDate`, `IsApproved`, `UserOrganizationId`) VALUES (1, 1, NOW(), 1, @UserOrganization);