﻿using MyAdaptiveCloud.Services.Apis.Tenax.Model;
using MyAdaptiveCloud.Services.Requests.Tenax;

namespace MyAdaptiveCloud.Services.Apis.Tenax
{
    public interface ITenaxApi
    {
        Task<List<TenaxTenant>> GetTenants(bool includeUsers = false);

        Task<TenaxTenant> CreateMSPTenant(string tenantName);

        Task<TenaxTenant> CreateTenant(string tenantName);

        Task<TenaxResultBatch<TenaxUser>> GetMSPUsers(string mspId);

        Task<TenaxResultBatch<TenaxUser>> GetTenantUsers(string tenantId);

        Task<List<string>> GetMSPRoles();

        Task<List<string>> GetTenantRoles();

        Task<HttpResponseMessage> CreateTenantUser(string tenantId, CreateUserRequest request);

        Task<HttpResponseMessage> CreateMSPUser(string mspId, CreateUserRequest request);

        Task<HttpResponseMessage> DeactivateTenantUser(string tenantId, string userId);

        Task<HttpResponseMessage> ActivateTenantUser(string tenantId, string userId);

        Task<HttpResponseMessage> DeactivateMSPUser(string mspId, string userId);

        Task<HttpResponseMessage> ActivateMSPUser(string mspId, string userId);

        Task<HttpResponseMessage> ReinviteTenantUser(string tenantId, string userId);

        Task<HttpResponseMessage> ReinviteMSPUser(string mspId, string userId);

        Task<HttpResponseMessage> UpdateMSPUser(string mspId, string userId, EditUserRequest request);

        Task<HttpResponseMessage> UpdateTenantUser(string tenantId, string userId, EditUserRequest request);

        Task<List<TenaxMSP>> GetMSPs(bool includeUsers = false);
    }
}