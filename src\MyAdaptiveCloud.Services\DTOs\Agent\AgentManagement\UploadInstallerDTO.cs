namespace MyAdaptiveCloud.Services.DTOs.Agent.AgentManagement
{
    public class UploadInstallerDTO
    {
        public string ServiceName { get; set; }
        public string Platform { get; set; }
        public int MajorVersion { get; set; }
        public int MinorVersion { get; set; }
        public int BuildVersion { get; set; }
        public string DefaultInstallerFilePath { get; set; }
        public string OriginalFileName { get; set; }
        public string FilePath { get; set; }
    }
}