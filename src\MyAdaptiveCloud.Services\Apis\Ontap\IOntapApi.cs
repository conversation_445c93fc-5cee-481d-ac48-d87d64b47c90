using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public interface IOntapApi
{
    string AggregateName { get; }
    string BasePortName { get; }
    PortApi Port();
    NodeApi Node();
    IpspaceApi Ipspace();
    BroadcastDomainApi BroadcastDomain();
    SvmApi Svm();
    CifsApi Cifs();
    StorageApi Storage();
    Task<Job> GetJob(Guid job);
    Task<Job> WaitOnJob(Job job);
    Task<Job> WaitOnJob(Guid job);
}