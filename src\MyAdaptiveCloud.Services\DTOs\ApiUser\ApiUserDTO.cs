﻿using MyAdaptiveCloud.Core.Common;

namespace MyAdaptiveCloud.Services.DTOs.ApiUser
{
    public class ApiUserDTO
    {
        public int PersonId { get; set; }

        public string Name { get; set; }

        public BasePerson CreatedBy { get; set; }

        public DateTimeOffset CreatedDate { get; set; }

        public bool CanDeleteUsers { get; set; }

        public bool CanViewUsers { get; set; }
    }
}
