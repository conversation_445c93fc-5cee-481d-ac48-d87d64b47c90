namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class BroadcastDomain : OntapObject
{
    public Ipspace Ipspace { get; set; }
    public List<Port> Ports { get; set; }
    public int? Mtu { get; set; }

    public BroadcastDomain() {}

    public BroadcastDomain(Guid uuid): base(uuid) { }
    public BroadcastDomain(string name, int mtu, Guid ipspaceUuid): base(name)
    {
        Mtu = mtu;
        Ipspace = new Ipspace(ipspaceUuid);
    }

    public override string ToString()
    {
        var portList = Ports != null ? String.Join(", ", Ports.ConvertAll(p => $"{p.Name} {p.Node.Name} {p.Uuid}")) : "";
        return $"Broadcast Domain Name: {Name} IpSpace: {Ipspace.Name} MTU: {Mtu} Ports: {portList}";
    }
}

public class UpdateBroadcastDomain {
    public UpdateBroadcastDomain() { }
    public string Name { get; set; }
    public UpdateBroadcastDomain(string name) { Name = name; }
}