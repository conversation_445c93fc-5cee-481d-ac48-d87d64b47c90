﻿using System.Globalization;
using CsvHelper.Configuration;

namespace MyAdaptiveCloud.Services.DTOs.AdaptiveCloud
{
    public class UsageSummaryAccountModelCSVMapping : ClassMap<UsageSummaryAccountModel>
    {
        public UsageSummaryAccountModelCSVMapping()
        {
            Map(m => m.AccountName).Index(0).Name("Account");
            Map(m => m.VCpu.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(1).Name("vCPU");
            Map(m => m.Ram.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(2).Name("Ram");
            Map(m => m.IpAddress.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(3).Name("IP Address");
            Map(m => m.NetworkBytes.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(4).Name("Network Bytes");
            Map(m => m.PrimaryStorage.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(5).Name("Primary Storage");
            Map(m => m.SecondaryStorage.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(6).Name("Secondary Storage");
            Map(m => m.Licensing.TotalCost).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(7).Name("Licensing");
            Map(m => m.AccountTotal).TypeConverterOption.NumberStyles(NumberStyles.Currency).TypeConverterOption.Format(new[] { "0.00" }).Index(8).Name("Total");
        }
    }
}
