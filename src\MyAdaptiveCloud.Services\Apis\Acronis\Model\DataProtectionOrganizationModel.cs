namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class DataProtectionOrganizationModel : IDataProtectionCredential
    {
        public int? OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public string MappingPrimaryId { get; set; }
        public string MappingSecondaryId { get; set; }
        public bool IsPartner { get; set; }
        public string ClientId { get; set; }
        public string SecretKey { get; set; }
        public AcronisToken token { get; set; }
        public string OrganizationParentFullPath { get; set; }
    }
}