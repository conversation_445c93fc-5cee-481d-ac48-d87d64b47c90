﻿namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class HealthStatDiskDTO
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public float? DiskUsage { get; set; }

        public float? SizeUsed { get; set; }

        public float? SizeTotal { get; set; }

        public int? WarningThreshold { get; set; }

        public int? ErrorThreshold { get; set; }

        public int? CriticalThreshold { get; set; }

        public int InheritanceType { get; set; }

        public string AlertThresholdTypeName { get; set; }
    }
}
