-- liquibase formatted sql

-- changeset voviedo:d4242149-e510-4104-a6a3-c8a53d32f621 context:main
ALTER TABLE IF EXISTS schedule ADD COLUMN IF NOT EXISTS StartDateTimeLocalized VARCHAR(255);

-- changeset voviedo:db8a5dd0-92ab-4074-ac4b-1bbae420f8d1 context:!dev
UPDATE schedule s
SET StartDateTimeLocalized = CONCAT(
        DATE_FORMAT(CONVERT_TZ(s.StartDate, '+00:00', s.StartDateTimezone), '%Y-%m-%dT%H:%i:%s'),
        TIME_FORMAT(
            TIMEDIFF(NOW(), CONVERT_TZ(NOW(), s.StartDateTimezone, 'UTC')), 
            '%H:%i'
        )
    );

-- changeset voviedo:d6ebc763-346d-4802-acb1-5ba7edba1411 context:dev
UPDATE schedule SET StartDate = '2024-11-05T22:18:00', StartDateTimeZone = 'America/Buenos_Aires', StartDateTimeLocalized = '2024-11-05T22:18:00-03:00';