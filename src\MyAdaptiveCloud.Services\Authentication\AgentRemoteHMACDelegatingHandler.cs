using System.Net.Http.Headers;
using System.Web;
using MyAdaptiveCloud.Core.Common.Cryptography.HMACSHA256;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Authentication
{
    public class AgentRemoteHMACDelegatingHandler : DelegatingHandler
    {
        private readonly string _apiKey;
        private readonly string _apiSecret;
        private readonly string _apiKeyHeaderName;
        private readonly string _timeStampHeaderName;
        private readonly string _authenticationMethodName;

        public AgentRemoteHMACDelegatingHandler(IConfigurationService configurationService)
        {
            var agentRemoteConfiguration = configurationService.GetAgentRemoteConfiguration().Result;

            _apiKey = agentRemoteConfiguration.ApiKey;
            _apiSecret = agentRemoteConfiguration.ApiSecret;

            _apiKeyHeaderName = "x-api-key";
            _timeStampHeaderName = "x-timestamp";
            _authenticationMethodName = "ACAR-HMAC-SHA256";
        }

        protected async override Task<HttpResponseMessage> SendAsync(HttpRequestMessage httpRequestMessage, CancellationToken cancellationToken)
        {
            var requestTimeStamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
            var nonce = Guid.NewGuid().ToString("N");

            var stringToSign = await GetStringToSign(httpRequestMessage, requestTimeStamp, nonce);
            var signingKey = MYACHMACSHA256Helper.GetSigningKey(_authenticationMethodName, _apiSecret, requestTimeStamp.ToString(), nonce);
            var signature = MYACHMACSHA256Helper.GetSignature(signingKey, stringToSign);

            httpRequestMessage.Headers.Authorization = new AuthenticationHeaderValue
            (_authenticationMethodName,
                string.Format("{0}:{1}:{2}", signature, nonce, requestTimeStamp));

            httpRequestMessage.Headers.Add(_apiKeyHeaderName, _apiKey);
            httpRequestMessage.Headers.Add(_timeStampHeaderName, requestTimeStamp.ToString());

            var response = await base.SendAsync(httpRequestMessage, cancellationToken);
            return response;
        }

        private async Task<string> GetPayloadHash(HttpRequestMessage httpRequestMessage)
        {
            if (httpRequestMessage.Content == null)
            {
                return string.Empty;
            }

            return MYACHMACSHA256Helper.GetHashFromBytes(await httpRequestMessage.Content.ReadAsByteArrayAsync());
        }

        private async Task<string> GetStringToSign(HttpRequestMessage httpRequestMessage, long requestTimeStamp, string nonce)
        {
            var requestUri = HttpUtility.UrlEncode(httpRequestMessage.RequestUri.LocalPath.ToLower());
            var requestHttpMethod = httpRequestMessage.Method.Method;

            var requestContentBase64String = await GetPayloadHash(httpRequestMessage);

            var canonicalRequest = string.Format("{0}{1}{2}{3}{4}",
                requestHttpMethod, requestUri, requestTimeStamp,
                nonce, requestContentBase64String);

            return $"{_authenticationMethodName}:{MYACHMACSHA256Helper.GetHashFromString(canonicalRequest)}";
        }
    }
}