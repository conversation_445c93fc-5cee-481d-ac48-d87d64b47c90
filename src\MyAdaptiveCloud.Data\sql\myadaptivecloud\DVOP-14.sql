-- liquibase formatted sql

-- changeset jbarrera:0CFDBB22-CFF2-4562-913E-63D9C60B0DEA context:"main"
ALTER TABLE IF EXISTS User
MODIFY COLUMN IF EXISTS `Email` VARCHAR(255) NOT NULL;

-- changeset jbarrera:7C3367BF-DE4F-4C55-ADD7-D0901E654453 runOnChange:true context:dev,test
SELECT UserId INTO @PersonId FROM User WHERE Email = '<EMAIL>' LIMIT 1;
INSERT IGNORE INTO `User_Organization` (`OrganizationId`, `UserId`) VALUES (0, @PersonId);
SELECT @UserOrganization := LAST_INSERT_ID();
INSERT IGNORE INTO `User_Organization_Mapping` (`RoleId`, `CreatedBy`, `CreatedDate`, `IsApproved`, `UserOrganizationId`) VALUES (1, 1, NOW(), 1, @UserOrganization);

-- changeset jbarrera:BBEEFED3-9778-4A39-989D-10ECC43FCCBB
ALTER TABLE IF EXISTS Role
ADD CONSTRAINT `FK_Role_OrganizationId_Organization_OrganizationId` FOREIGN KEY IF NOT EXISTS (OrganizationId) REFERENCES `Organization` (OrganizationId);