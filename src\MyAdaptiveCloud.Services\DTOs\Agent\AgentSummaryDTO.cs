using MyAdaptiveCloud.Core.Common.Agent;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Alerts;
using MyAdaptiveCloud.Services.DTOs.AlertRules;
using MyAdaptiveCloud.Services.DTOs.DeviceThresholds;
using MyAdaptiveCloud.Services.Helper;
using MyAdaptiveCloud.Services.Services.DeviceAlerts;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public enum Status : short
    {
        Green = 1,
        Yellow = 2,
        Orange = 3,
        Red = 4
    }

    public class AgentSummaryDTO
    {
        public int AgentId { get; set; }
        public Guid AgentUuid { get; set; }
        public string Hostname { get; set; }
        public string Description { get; set; }
        public Status Status { get; set; }
        public string Ip { get; set; }
        public string OperatingSystem { get; set; }
        public DateTimeOffset? HeartbeatTs { get; set; }
        public DateTimeOffset? HealthStatTs { get; set; }
        public int? CpuUsage { get; set; }
        public float? RamTotal { get; set; }
        public float? RamUsed { get; set; }
        public float? RamUsage { get; set; }
        public int AgentVersionMajor { get; set; }
        public int AgentVersionMinor { get; set; }
        public int AgentVersionBuild { get; set; }
        public string FolderIdsPathFromOrganization { get; set; }

        /// <summary>
        /// This property is used to determine if the agent is capable of remote desktop functionality.
        /// </summary>
        /// <remarks>
        /// This is here primarily to prevent users from generating 
        private bool? _canRemoteDesktop;

        public bool? CanRemoteDesktop
        {
            get
            {
                if (_canRemoteDesktop == null)
                {
                    _canRemoteDesktop = AgentActionAvailabilityHelper.IsAgentActionAvailable(
                        AgentActionTypeEnum.RemoteDesktopConnectionInitiate,
                        AgentVersionMajor,
                        AgentVersionMinor,
                        AgentVersionBuild
                    );
                }

                return _canRemoteDesktop;
            }
            set { _canRemoteDesktop = value; }
        }

        /// <summary>
        /// This property is used to determine if the agent is capable of remote commands functionality.
        /// </summary>
        /// <remarks>
        private bool? _canRemoteCommands;

        public bool? CanRemoteCommands
        {
            get
            {
                if (_canRemoteCommands == null)
                {
                    _canRemoteCommands = AgentActionAvailabilityHelper.IsAgentActionAvailable(
                        AgentActionTypeEnum.RemoteCommandConnect,
                        AgentVersionMajor,
                        AgentVersionMinor,
                        AgentVersionBuild
                    );
                }

                return _canRemoteCommands;
            }
            set { _canRemoteCommands = value; }
        }

        /// <summary>
        /// When this property is true, means the agent is capable of uninstall/reset
        /// </summary>
        /// 
        public bool CanDecommissionDevice
        {
            get { return AgentActionAvailabilityHelper.IsAgentActionAvailable(AgentActionTypeEnum.Decommission, AgentVersionMajor, AgentVersionMinor, AgentVersionBuild); }
        }

        public string DiskName { get; set; }
        public float? SizeTotal { get; set; }
        public float? SizeUsed { get; set; }
        public float? DiskUsage { get; set; }

        public List<HealthStatDiskDTO> Disks { get; set; } = new List<HealthStatDiskDTO>();

        public int OrganizationId { get; set; }
        public string Organization { get; set; }
        public int? FolderId { get; set; }
        public DateTimeOffset? UptimeLastStartTimeUtc { get; set; }

        public string UptimeForDisplayDaysHoursMinutes { get; set; }

        public int? RamUsageWarningThreshold { get; set; }
        public int? RamUsageErrorThreshold { get; set; }
        public int? RamUsageCriticalThreshold { get; set; }
        public string RamUsageAlertThresholdTypeName { get; set; }

        public DeviceAlertThresholdInheritanceTypeEnum RamUsageInheritanceType { get; set; }

        public int? CPUUsageWarningThreshold { get; set; }
        public int? CPUUsageErrorThreshold { get; set; }
        public int? CPUUsageCriticalThreshold { get; set; }
        public DeviceAlertThresholdInheritanceTypeEnum CPUUsageInheritanceType { get; set; }


        public int? HeartbeatWarningThreshold { get; set; }
        public int? HeartbeatErrorThreshold { get; set; }
        public int? HeartbeatCriticalThreshold { get; set; }
        public DeviceAlertThresholdInheritanceTypeEnum HeartbeatInheritanceType { get; set; }

        public List<DeviceAlertDTO> ActiveAlerts { get; set; } = [];

        public bool InScheduleDowntime { get; set; }

        public DateTimeOffset? ScheduleDowntimeEndDate { get; set; }

        public string AcknowledgedBy { get; set; }

        public DateTimeOffset? AcknowledgedDate { get; set; }

        public bool MarkedForUninstall { get; set; }

        public bool MarkedForReRegister { get; set; }

        public bool CloudStackVM { get; set; }

        public string LastLogonUsername { get; set; }

        private int? GetAgentUnreachableThreshold()
        {
            return HeartbeatCriticalThreshold ?? HeartbeatErrorThreshold ?? HeartbeatWarningThreshold ?? null;
        }

        private readonly Dictionary<AlertThresholdLevelEnumDTO, Status> StatusByThresholdLevel = new Dictionary<AlertThresholdLevelEnumDTO, Status>
        {
            { AlertThresholdLevelEnumDTO.Warning, Status.Yellow },
            { AlertThresholdLevelEnumDTO.Error, Status.Orange },
            { AlertThresholdLevelEnumDTO.Critical, Status.Red }
        };

        public void ComputeStatus()
        {
            DefineStatusAlert();
            PopulateUptimeForDisplayDaysHoursMinutes();
        }

        private Status DefineStatusAlert()
        {
            Status = Status.Green;

            if (ActiveAlerts?.Any() ?? false)
            {
                var greatestThresholdLevel = ActiveAlerts.Select(s => s.AlertThresholdLevel).Distinct().Max();
                Status = StatusByThresholdLevel[greatestThresholdLevel];
            }

            return Status;
        }

        private void PopulateUptimeForDisplayDaysHoursMinutes()
        {
            if (HeartbeatTs == null)
            {
                UptimeForDisplayDaysHoursMinutes = "down";
                return;
            }

            var dateNowUtc = DateTime.UtcNow;

            var agentUnreachableThreshold = GetAgentUnreachableThreshold();

            var isAgentUnreachable =
                (agentUnreachableThreshold.HasValue &&
                 DateTime.UtcNow.AddSeconds(-agentUnreachableThreshold.Value) > HeartbeatTs)
                ||
                (!agentUnreachableThreshold.HasValue &&
                 (DateTime.UtcNow - HeartbeatTs).Value.TotalSeconds > DeviceAlertAgentDownManager.DELAY_TO_CONSIDER_IMMEDIATE_HEARTBEAT);

            if (UptimeLastStartTimeUtc == null || isAgentUnreachable)
            {
                UptimeForDisplayDaysHoursMinutes = "down";
                return;
            }

            var uptimeLastStartTimeUtcDateTime = UptimeLastStartTimeUtc ?? DateTime.MinValue;
            TimeSpan timeDifference = dateNowUtc - uptimeLastStartTimeUtcDateTime; // calculate time difference

            string timeDifferenceString = $"{(int)timeDifference.TotalDays} Days {timeDifference.Hours:D2}:{timeDifference.Minutes:D2}"; // format time difference as string
            UptimeForDisplayDaysHoursMinutes = timeDifferenceString;
        }

        public void SetThresholds(DeviceAlertTypeEnumDTO alertType, DeviceComponentExternalThresholdsDTO thresholds)
        {
            var thresholdType = thresholds.DeviceAlertThresholdTypes.Find(t => t.Id == thresholds.DeviceAlertThresholdType);
            switch (alertType)
            {
                case DeviceAlertTypeEnumDTO.Memory:
                    RamUsageInheritanceType = (DeviceAlertThresholdInheritanceTypeEnum)thresholds.InheritanceType;
                    RamUsageWarningThreshold = thresholds.Metrics.Warning;
                    RamUsageCriticalThreshold = thresholds.Metrics.Critical;
                    RamUsageErrorThreshold = thresholds.Metrics.Error;
                    RamUsageAlertThresholdTypeName = thresholdType?.Name;
                    break;
                case DeviceAlertTypeEnumDTO.Heartbeat:
                    HeartbeatInheritanceType = (DeviceAlertThresholdInheritanceTypeEnum)thresholds.InheritanceType;
                    if (thresholds.Metrics.Warning.HasValue)
                    {
                        HeartbeatWarningThreshold = (int)thresholds.Metrics.Warning;
                    }

                    if (thresholds.Metrics.Critical.HasValue)
                    {
                        HeartbeatCriticalThreshold = (int)thresholds.Metrics.Critical;
                    }

                    if (thresholds.Metrics.Error.HasValue)
                    {
                        HeartbeatErrorThreshold = (int)thresholds.Metrics.Error;
                    }

                    break;
                case DeviceAlertTypeEnumDTO.CPU:
                    CPUUsageInheritanceType = (DeviceAlertThresholdInheritanceTypeEnum)thresholds.InheritanceType;
                    CPUUsageWarningThreshold = thresholds.Metrics.Warning;
                    CPUUsageCriticalThreshold = thresholds.Metrics.Critical;
                    CPUUsageErrorThreshold = thresholds.Metrics.Error;
                    break;
            }
        }

        public void AssignThresholdsToAgent(DeviceThresholdsDTO threshold)
        {
            SetThresholds(DeviceAlertTypeEnumDTO.CPU, threshold.CpuThresholds);
            SetThresholds(DeviceAlertTypeEnumDTO.Memory, threshold.MemoryThresholds);
            SetThresholds(DeviceAlertTypeEnumDTO.Heartbeat, threshold.AgentThresholds);

            foreach (var disk in Disks)
            {
                // Uses own thresholds or all drives thresholds
                var current = threshold.DiskThresholds.Find(x => x.Name == disk.Name);
                if (current != null)
                {
                    var alertThresholdType = current.DeviceAlertThresholdTypes.Find(t => t.Id == current.DeviceAlertThresholdType);
                    disk.AlertThresholdTypeName = alertThresholdType?.Name;
                    disk.InheritanceType = current.InheritanceType;
                    disk.WarningThreshold = current.Metrics.Warning;
                    disk.ErrorThreshold = current.Metrics.Error;
                    disk.CriticalThreshold = current.Metrics.Critical;
                }
            }
        }

        public void SetAcknowledgedByAndDate()
        {
            var acknowledgeDistinct = ActiveAlerts.Select(s => new { s.AcknowledgedBy, s.AcknowledgedDate }).Distinct().ToList();
            if (acknowledgeDistinct.Count == 1)
            {
                AcknowledgedBy = acknowledgeDistinct.First().AcknowledgedBy;
                AcknowledgedDate = acknowledgeDistinct.First().AcknowledgedDate;
            }
        }
    }
}