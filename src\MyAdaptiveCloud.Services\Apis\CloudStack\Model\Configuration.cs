﻿using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;
using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class Configuration
    {
        public string Category { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public string Description { get; set; }
        public bool IsDynamic { get; set; }
    }

    public class ListConfigurationsResponse : CloudStackListResponse<Configuration>
    {
        [JsonPropertyName("configuration")]
        public override List<Configuration> Items { get; set; } = new List<Configuration>();
    }
}