﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public class ApproveUpdateActionDTO
    {
        [JsonPropertyName("ext_update_id")]
        public string ExtUpdateId { get; set; }

        [JsonPropertyName("next_install_date")]
        public DateTimeOffset NextInstallDate { get; set; }

        [JsonPropertyName("next_install_date_time_zone")]
        public string NextInstallDateTimeZone { get; set; }

        [JsonPropertyName("install_immediately")]
        public bool InstallImmediately { get; set; }
    }
}