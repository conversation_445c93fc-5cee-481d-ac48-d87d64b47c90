﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Alerts;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Policies;
using MyAdaptiveCloud.Services.DTOs.DeviceAlerts;
using MyAdaptiveCloud.Services.DTOs.DeviceAlerts.AlertGeneration;
using MyAdaptiveCloud.Services.DTOs.DeviceFolder;
using MyAdaptiveCloud.Services.DTOs.DeviceThresholds;

namespace MyAdaptiveCloud.Services.AutoMapper
{
    public class DeviceFoldersMapperProfile : Profile
    {
        public DeviceFoldersMapperProfile()
        {
            CreateMap<Tuple<DeviceFolder, DeviceFolderPolicyConfiguration>, DeviceFolderDTO>()
                .ForMember(d => d.FolderId, opt => opt.MapFrom(s => s.Item1.FolderId))
                .ForMember(d => d.ParentFolderId, opt => opt.MapFrom(s => s.Item1.ParentFolderId))
                .ForMember(d => d.Name, opt => opt.MapFrom(s => s.Item1.Name))
                .ForMember(d => d.Description, opt => opt.MapFrom(s => s.Item1.Description))
                .ForMember(d => d.OrganizationId, opt => opt.MapFrom(s => s.Item1.OrganizationId))
                .ForMember(d => d.PolicyId, opt => opt.MapFrom(s => s.Item2 != null ? s.Item2.PolicyId : null))
                .ForMember(d => d.InheritsPolicy, opt => opt.MapFrom(s => s.Item2 == null))
                .ForMember(d => d.PolicyInheritedFrom, opt => opt.Ignore())
                .ForMember(d => d.HasSubfolders, opt => opt.Ignore())
                .ForMember(d => d.DeviceCount, opt => opt.Ignore())
                .ForMember(d => d.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<DeviceFolder, DeviceFolderDTO>()
                .ForMember(d => d.PolicyId, opt => opt.Ignore())
                .ForMember(d => d.InheritsPolicy, opt => opt.Ignore())
                .ForMember(d => d.PolicyInheritedFrom, opt => opt.Ignore())
                .ForMember(d => d.HasSubfolders, opt => opt.Ignore())
                .ForMember(d => d.DeviceCount, opt => opt.Ignore())
                .ForMember(d => d.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<DeviceFolder, DeviceFolderTreeNodeDTO>()
                .ForMember(d => d.HasSubfolders, opt => opt.Ignore())
                .ForMember(d => d.DeviceCount, opt => opt.Ignore())
                .ForMember(d => d.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<FolderHierarchy, DeviceFolderHierarchyDTO>()
                .ForMember(d => d.DeviceFolderType, opt => opt.Ignore())
                .ForMember(d => d.OrganizationId, opt => opt.Ignore())
                .ForMember(d => d.Hierarchy, opt => opt.Ignore());

            CreateMap<DeviceAlertThresholdType, DeviceAlertThresholdTypeDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId));

            CreateMap<DeviceAlertThresholdTypeDTO, DeviceAlertThresholdType>()
                .ForMember(dest => dest.DeviceAlertThresholdTypeId, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore());

            CreateMap<DeviceComponentExternalThresholdsDTO, DeviceComponentThresholdsDTO>()
                .ForMember(dest => dest.RealInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceThresholdLevelId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceThresholdLevel, opt => opt.Ignore());

            CreateMap<DeviceThresholdsDTO, DeviceThresholdsExternalDTO>();
            CreateMap<DeviceThresholdsExternalDTO, DeviceThresholdsDTO>();

            CreateMap<DeviceAlertThresholdInheritedValues, DeviceComponentThresholdsDTO>()
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceThresholdLevel, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceThresholdLevelId, opt => opt.Ignore())
                .ForMember(dest => dest.InheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.InheritedFromPath, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertThresholdTypes, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertType, opt => opt.Ignore())
                .ForMember(dest => dest.RealInheritanceType, opt => opt.MapFrom(src => src.InheritanceTypeId))
                .ForMember(dest => dest.InheritFrom, opt => opt.MapFrom(src => src.InheritedFrom))
                .ForMember(dest => dest.DeviceAlertThresholdType, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId))
                .ForPath(dest => dest.Metrics.Warning, opt => opt.MapFrom(src => src.AlertThresholdWarning))
                .ForPath(dest => dest.Metrics.Error, opt => opt.MapFrom(src => src.AlertThresholdError))
                .ForPath(dest => dest.Metrics.Critical, opt => opt.MapFrom(src => src.AlertThresholdCritical))
                .ForPath(dest => dest.Intervals.Trigger, opt => opt.MapFrom(src => src.TriggerAfterMinutes))
                .ForPath(dest => dest.Intervals.Clear, opt => opt.MapFrom(src => src.ClearAfterMinutes));

            CreateMap<OrganizationDeviceAlertThresholdDTO, DeviceComponentThresholdsDTO>()
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.InheritedFromPath, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertThresholdTypes, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertType, opt => opt.Ignore())
                .ForMember(dest => dest.RealInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.InheritFrom, opt => opt.Ignore())
                .ForMember(dest => dest.InheritanceType, opt => opt.MapFrom(src => src.DeviceAlertThresholdInheritanceTypeId))
                .ForMember(dest => dest.DeviceAlertThresholdType, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId))
                .ForMember(dest => dest.Intervals, opt => opt.MapFrom(src => new DeviceThresholdIntervalsDTO
                {
                    Clear = src.ClearAfterMinutes,
                    Trigger = src.TriggerAfterMinutes
                }))
                .ForMember(dest => dest.Metrics, opt => opt.MapFrom(src => new DeviceThresholdsMetricsDTO
                {
                    Warning = src.AlertThresholdWarning,
                    Critical = src.AlertThresholdCritical,
                    Error = src.AlertThresholdError
                }))
                .ForMember(dest => dest.DeviceThresholdLevel, opt => opt.MapFrom(_ => ThresholdLevelDTO.Organization))
                .ForMember(dest => dest.DeviceThresholdLevelId, opt => opt.MapFrom(src => src.OrganizationDeviceAlertThresholdId));


            CreateMap<FolderDeviceAlertThresholdDTO, DeviceComponentThresholdsDTO>()
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.InheritedFromPath, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertThresholdTypes, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertType, opt => opt.Ignore())
                .ForMember(dest => dest.RealInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.InheritFrom, opt => opt.Ignore())
                .ForMember(dest => dest.InheritanceType, opt => opt.MapFrom(src => src.DeviceAlertThresholdInheritanceTypeId))
                .ForMember(dest => dest.DeviceAlertThresholdType, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId))
                .ForMember(dest => dest.Intervals, opt => opt.MapFrom(src => new DeviceThresholdIntervalsDTO
                {
                    Clear = src.ClearAfterMinutes,
                    Trigger = src.TriggerAfterMinutes
                }))
                .ForMember(dest => dest.Metrics, opt => opt.MapFrom(src => new DeviceThresholdsMetricsDTO
                {
                    Warning = src.AlertThresholdWarning,
                    Critical = src.AlertThresholdCritical,
                    Error = src.AlertThresholdError
                }))
                .ForMember(dest => dest.DeviceThresholdLevel, opt => opt.MapFrom(_ => ThresholdLevelDTO.Folder))
                .ForMember(dest => dest.DeviceThresholdLevelId, opt => opt.MapFrom(src => src.FolderDeviceAlertThresholdId));

            CreateMap<DeviceAlertThresholdOverride, DeviceAlertThresholdOverrideDTO>()
                .ForMember(dest => dest.DeviceAlertTypeId, opt => opt.MapFrom(src => src.DeviceAlertTypeId))
                .ForMember(dest => dest.DeviceAlertThresholdTypeId, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId))
                .ForMember(dest => dest.DeviceAlertThresholdInheritanceTypeId, opt => opt.MapFrom(src => src.DeviceAlertThresholdInheritanceTypeId))
                .ForMember(dest => dest.AlertThresholdWarning, opt => opt.MapFrom(src => src.AlertThresholdWarning))
                .ForMember(dest => dest.AlertThresholdError, opt => opt.MapFrom(src => src.AlertThresholdError))
                .ForMember(dest => dest.AlertThresholdCritical, opt => opt.MapFrom(src => src.AlertThresholdCritical))
                .ForMember(dest => dest.TriggerAfterMinutes, opt => opt.MapFrom(src => src.TriggerAfterMinutes))
                .ForMember(dest => dest.ClearAfterMinutes, opt => opt.MapFrom(src => src.ClearAfterMinutes))
                .ForMember(dest => dest.DeviceAlertThresholdOverrideId, opt => opt.MapFrom(src => src.DeviceAlertThresholdId))
                .ForMember(dest => dest.DeviceId, opt => opt.MapFrom(src => src.DeviceId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

            CreateMap<DeviceAlertThresholdOverrideDTO, DeviceComponentThresholdsDTO>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.InheritedFromPath, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertThresholdTypes, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertType, opt => opt.Ignore())
                .ForMember(dest => dest.RealInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.InheritFrom, opt => opt.Ignore())
                .ForMember(dest => dest.InheritanceType, opt => opt.MapFrom(src => src.DeviceAlertThresholdInheritanceTypeId))
                .ForMember(dest => dest.DeviceAlertThresholdType, opt => opt.MapFrom(src => src.DeviceAlertThresholdTypeId))
                .ForMember(dest => dest.Intervals, opt => opt.MapFrom(src => new DeviceThresholdIntervalsDTO
                {
                    Clear = src.ClearAfterMinutes,
                    Trigger = src.TriggerAfterMinutes
                }))
                .ForMember(dest => dest.Metrics, opt => opt.MapFrom(src => new DeviceThresholdsMetricsDTO
                {
                    Warning = src.AlertThresholdWarning,
                    Critical = src.AlertThresholdCritical,
                    Error = src.AlertThresholdError
                }))
                .ForMember(dest => dest.DeviceThresholdLevel, opt => opt.MapFrom(_ => ThresholdLevelDTO.AgentOverride))
                .ForMember(dest => dest.DeviceThresholdLevelId, opt => opt.MapFrom(src => src.DeviceAlertThresholdOverrideId));

            CreateMap<DeviceFolder, DeviceFolderForAlertsDTO>()
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.ParentFolderId));

            CreateMap<DeviceFolderForAlertsDTO, DeviceFolder>()
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.ParentFolderId, opt => opt.MapFrom(src => src.ParentId))
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore())
                .ForMember(dest => dest.ParentFolder, opt => opt.Ignore())
                .ForMember(dest => dest.Organization, opt => opt.Ignore());

            CreateMap<DeviceFolder, BaseFolderDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.ParentFolderId, opt => opt.MapFrom(src => src.ParentFolderId));

            CreateMap<DeviceFolder, HierarchyFolderDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.FolderId, opt => opt.MapFrom(src => src.FolderId))
                .ForMember(dest => dest.ParentFolderId, opt => opt.MapFrom(src => src.ParentFolderId))
                .ForMember(dest => dest.ParentFolder, opt => opt.MapFrom(src => src.ParentFolder))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

            CreateMap<Policy, BasePolicyDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrganizationId))
                .ForMember(dest => dest.PolicyId, opt => opt.MapFrom(src => src.PolicyId))
                .ForMember(dest => dest.CategoriesToAutoApprove, opt => opt.MapFrom(src => src.UpdateCategoriesAutoApproval));
        }
    }
}