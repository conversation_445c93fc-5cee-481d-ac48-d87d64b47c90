﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Contacts;

namespace MyAdaptiveCloud.Services.AutoMapper.Contacts
{
    public class ContactMapperProfile : Profile
    {
        public ContactMapperProfile()
        {
            CreateMap<Contact, ContactDTO>()
                .ForMember(dest => dest.ContactId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Person, opt => opt.MapFrom(src => src.Person));
        }
    }
}
