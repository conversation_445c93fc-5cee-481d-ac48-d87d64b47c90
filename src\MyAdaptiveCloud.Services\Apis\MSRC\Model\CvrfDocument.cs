using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Msrc.Model
{
    public class CvrfDocument
    {
        [JsonPropertyName("Vulnerability")]
        public List<Vulnerability> Vulnerabilities { get; set; } = [];
    }

    public class Vulnerability
    {
        [JsonPropertyName("CVE")]
        public string cveId { get; set; }

        [JsonPropertyName("Title")]
        public LocalizedString Title { get; set; }

        [JsonPropertyName("Threats")]
        public List<Threat> Threats { get; set; }

        [JsonPropertyName("Remediations")]
        public List<Remediation> Remediations { get; set; }
    }

    public class Threat
    {
        [JsonPropertyName("Type")]
        public int Type { get; set; } // 3 = Severity

        [JsonPropertyName("Description")]
        public LocalizedString Description { get; set; }

        [JsonPropertyName("ProductID")]
        public List<string> ProductID { get; set; }
    }

    public class Remediation
    {
        [JsonPropertyName("Description")]
        public LocalizedString Description { get; set; }

        [JsonPropertyName("URL")]
        public string Url { get; set; }

        [JsonPropertyName("ProductID")]
        public List<string> ProductIds { get; set; }

        [JsonPropertyName("Type")]
        public int Type { get; set; } // 2 = Vendor Fix

        [JsonPropertyName("SubType")]
        public string SubType { get; set; } // "Security Update"
    }

    public class LocalizedString
    {
        [JsonPropertyName("Value")]
        public string Value { get; set; }
    }
}