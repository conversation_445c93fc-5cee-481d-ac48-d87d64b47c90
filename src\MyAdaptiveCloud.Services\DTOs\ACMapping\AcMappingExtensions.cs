﻿using MyAdaptiveCloud.Data.MyAdaptiveCloud;

namespace MyAdaptiveCloud.Services.DTOs.ACMapping
{
    public static class AcMappingExtensions
    {
        public static ACMappingDTO ToAcMappingModel(this AcCwAccountMap acMapping)
        {
            return new ACMappingDTO
            {
                Id = acMapping.Id,
                AcId = acMapping.AcId,
                AcName = acMapping.AcName,
                AcType = acMapping.AcType,
                CwCompanyId = acMapping.CwCompanyId,
                CwCompanyName = acMapping.CwCompanyName,
                CwCompanyIdentifier = acMapping.CwCompanyIdentifier,
                CwAgreementId = acMapping.CwAgreementId,
                CwAgreementName = acMapping.CwAgreementName,
                Enabled = acMapping.Enabled,
                BillingStartDate = acMapping.BillingStartDate,
                HasFutureBillingDate = HasFutureBillingDate(acMapping.BillingStartDate),
            };
        }

        public static IEnumerable<ACMappingDTO> ToAcMappingViewModel(this IEnumerable<AcCwAccountMap> acMappings)
        {
            return acMappings.Select(acMapping => acMapping.ToAcMappingModel());
        }


        public static void SetBillingStatus(this ACMappingDTO aCMappingDTO)
        {
            switch (aCMappingDTO.Id)
            {
                case null:
                    aCMappingDTO.BillingStatus = BillingStatusDTO.NewDomain;
                    break;
                default:
                    if (aCMappingDTO.Enabled && aCMappingDTO.HasFutureBillingDate)
                    {
                        aCMappingDTO.BillingStatus = BillingStatusDTO.BillingFuture;
                    }
                    else if (!aCMappingDTO.Enabled && aCMappingDTO.Id != null)
                    {
                        aCMappingDTO.BillingStatus = BillingStatusDTO.NoBilling;
                    }
                    else if (aCMappingDTO.Enabled && !aCMappingDTO.HasFutureBillingDate)
                    {
                        aCMappingDTO.BillingStatus = BillingStatusDTO.Billing;
                    }

                    break;
            }
        }

        private static bool HasFutureBillingDate(DateTimeOffset? dateBillingStartDate)
        {
            return dateBillingStartDate > DateTimeOffset.UtcNow;
        }
    }
}