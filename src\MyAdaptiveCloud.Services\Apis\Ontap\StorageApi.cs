using MyAdaptiveCloud.Services.Apis.Ontap.Model;
using MyAdaptiveCloud.Services.DTOs.Configuration;
using FileInfo = MyAdaptiveCloud.Services.Apis.Ontap.Model.FileInfo;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class StorageApi : TypeApi
{
    public StorageApi(OntapApi client) : base(client)
    {
    }

    public async Task<List<Aggregate>> GetAggregates()
    {
        return await Client.GetResources<Aggregate>(UrlBuilder.ListUrl("/storage/aggregates"));
    }

    public async Task<List<Aggregate>> GetAggregates(string namePrefix)
    {
        return await Client.GetResources<Aggregate>(UrlBuilder.ListUrl("/storage/aggregates").AddQueryParam("name", namePrefix + "*"));
    }

    public async Task<List<Volume>> GetVolumes()
    {
        return await Client.GetResources<Volume>(UrlBuilder.ListUrl("/storage/volumes"));
    }

    public async Task<List<Volume>> GetVolumesBySvm(Guid svmId, bool includeIsRootVolume = false)
    {
        return await Client.GetResources<Volume>(UrlBuilder.ListUrl("/storage/volumes").AddQueryParam("svm.uuid", svmId)
            .AddQueryParam("is_svm_root", includeIsRootVolume ? "true" : "false"));
    }

    public async Task<List<Volume>> GetVolumesForSvms(List<Guid> svmIds)
    {
        var svmQuery = string.Join(",", svmIds);
        return await Client.GetResources<Volume>(UrlBuilder.ListUrl("/storage/volumes").AddQueryParam("svm.uuid", svmQuery));
    }

    public async Task<Volume> GetVolume(Guid uuid)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%uuid%", "uuid", uuid.ToString());
        return await Client.GetResource<Volume>(url);
    }

    public async Task<Volume> GetVolume(string name)
    {
        return await Client.SearchResource<Volume>(UrlBuilder.ListUrl("/storage/volumes").AddQueryParam("name", name));
    }

    public async Task<ListResponse<Volume>> CreateVolume(NetworkFileServersConfigurationDTO config, string name, string mountPath, long size, Guid svmId, string aggregate,
        bool autoSize, string snapshotPolicy)
    {
        var body = new Volume(svmId, aggregate, name.ToLowerInvariant(), mountPath, size, autoSize, snapshotPolicy, config);
        return await Client.CreateResourceAsync(new UrlBuilder("/storage/volumes"), body);
    }

    public async Task UpdateVolume(NetworkFileServersConfigurationDTO config, Guid volumeId, bool autoSize, long size, string snapshotPolicy, bool updateSize)
    {
        var body = new VolumeUpdate(size, autoSize, snapshotPolicy, updateSize, config);

        var url = UrlBuilder.ListUrl("/storage/volumes").AddQueryParam("uuid", volumeId.ToString());
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            var errorMessage = await Utilities.ExtractErrorMessage(response);

            throw new OntapApiException($"Unable to update Volume '{volumeId.ToString()}'. Error: {errorMessage}");
        }
    }

    public async Task UpdateVolumeName(Guid volumeId, string name)
    {
        var body = new VolumeUpdateName(name.ToLowerInvariant());

        var url = UrlBuilder.ListUrl("/storage/volumes").AddQueryParam("uuid", volumeId.ToString());
        var response = await Client.ExecutePatchRequest(url, body);
        if (!response.IsSuccessStatusCode)
        {
            throw new OntapApiException($"Unable to update Volume name'{volumeId.ToString()}'");
        }
    }

    public async Task<Job> DeleteVolume(Volume volume)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/storage/volumes", volume.Uuid));
    }

    public async Task<List<FileInfo>> GetFiles(Volume volume, string path)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path);
        return await Client.GetResources<FileInfo>(url);
    }

    public async Task<List<FileInfo>> GetDirectories(Volume volume, string path)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path).AddQueryParam("type", "directory");
        return await Client.GetResources<FileInfo>(url);
    }

    public async Task<FileInfo> GetFile(Volume volume, string path, string name)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path);
        return (await Client.GetResources<FileInfo>(url)).First();
    }

    public async Task<FileInfo> CreateDirectory(Volume volume, string path)
    {
        var body = new FileInfo(FileType.directory, 777);
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path.ToLowerInvariant());
        return await Client.CreateResource(url, body);
    }

    public async Task<Job> DeleteFile(Volume volume, string path)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path);
        return await Client.DeleteResourceAsync(url);
    }

    public async Task<Job> DeleteFolderRecursively(Volume volume, string path)
    {
        var url = UrlBuilder.ListUrl("/storage/volumes/%volume%/files/%path%", "volume", volume.Uuid.ToString(), "path", path).AddQueryParam("recurse", "true");
        return await Client.DeleteResourceAsync(url);
    }

    public async Task<List<SnapshotPolicy>> ListSnapshotPoliciesForCluster()
    {
        var url = UrlBuilder.ListUrl("/storage/snapshot-policies").AddQueryParam("scope", "cluster");
        return await Client.GetResources<SnapshotPolicy>(url);
    }

    public async Task<List<SnapshotPolicy>> ListSnapshotPolicies(Guid svmId)
    {
        var url = UrlBuilder.ListUrl("/storage/snapshot-policies").AddQueryParam("svm.uuid", svmId);
        return await Client.GetResources<SnapshotPolicy>(url);
    }

    public async Task<SnapshotPolicy> CreateSnapshotPolicy(Guid svmId, string newName, SnapshotPolicy source)
    {
        var body = new SnapshotPolicy(newName.ToLowerInvariant(), true, svmId);
        body.Copies = source.Copies.Select(c =>
        {
            var snapMirrorLabel = c.SnapmirrorLabel != "-" ? c.SnapmirrorLabel : c.Schedule.Name;
            return new SnapshotCopy(c.Schedule.Name.ToLowerInvariant(), c.Prefix, c.Count, snapMirrorLabel);
        }).ToList();
        return await Client.CreateResource(new UrlBuilder("/storage/snapshot-policies"), body);
    }

    public async Task<SnapshotPolicy> CreateSnapshotPolicy(Guid svmId, string name, string schedule)
    {
        var snapshotPolicy = new SnapshotPolicy(name, true, svmId);
        switch (schedule)
        {
            case "weekly":
                snapshotPolicy.AddCopySchedule(6, "hourly", "hourly", null);
                snapshotPolicy.AddCopySchedule(2, "daily", "daily", "daily");
                snapshotPolicy.AddCopySchedule(1, "weekly", "weekly", "weekly");
                break;
            case "monthly":
                snapshotPolicy.AddCopySchedule(6, "hourly", "hourly", null);
                snapshotPolicy.AddCopySchedule(2, "daily", "daily", "daily");
                snapshotPolicy.AddCopySchedule(2, "weekly", "weekly", "weekly");
                snapshotPolicy.AddCopySchedule(1, "monthly", "monthly", "monthly");
                break;
            default:
                throw new OntapApiException("Unknown Snapshot Policy Schedule");
        }

        var url = UrlBuilder.ListUrl("/storage/snapshot-policies");
        return await Client.CreateResource(url, snapshotPolicy);
    }
}