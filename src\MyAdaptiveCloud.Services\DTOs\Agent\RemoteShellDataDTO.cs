﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.DTOs.Agent
{
    public enum RemoteShellType
    {
        Cmd,
        PowerShell
    }

    public enum RemoteShellConnectionType
    {
        Client,
        Server
    }


    public class RemoteShellDataDTO
    {
        [JsonPropertyName("agent_session_url")]
        public string Url { get; set; }

        [JsonPropertyName("shell")]
        public string Shell { get; set; } // cmd.exe, powershell.exe
    }
}