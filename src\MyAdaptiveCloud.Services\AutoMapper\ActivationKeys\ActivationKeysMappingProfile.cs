﻿using AutoMapper;
using MyAdaptiveCloud.Data.Agent;
using MyAdaptiveCloud.Services.DTOs.ActivationKeys;

namespace MyAdaptiveCloud.Services.AutoMapper.ActivationKeys
{
    public class ActivationKeysMappingProfile : Profile
    {
        public ActivationKeysMappingProfile()
        {
            CreateMap<ActivationKey, ActivationKeyDTO>()
                .ForMember(dest => dest.OrganizationId, opt => opt.MapFrom(src => src.OrgId))
                .ForMember(dest => dest.Key, opt => opt.MapFrom(src => src.KeyVal))
                .ForMember(dest => dest.ActivationKeyId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.CanView, opt => opt.Ignore())
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanCreate, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore());
        }
    }
}