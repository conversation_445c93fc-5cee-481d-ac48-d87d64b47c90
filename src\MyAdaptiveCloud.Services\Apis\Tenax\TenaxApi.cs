﻿using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Services.Apis.Tenax.Model;
using MyAdaptiveCloud.Services.DTOs.Configuration;
using MyAdaptiveCloud.Services.Requests.Tenax;
using MyAdaptiveCloud.Services.Services;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.Tenax
{
    public class TenaxApi : ITenaxApi
    {
        private readonly HttpClient _client;
        private readonly IConfigurationService _configurationService;
        private readonly ILogger _logger;
        private readonly JsonSerializerOptions _jsonSerializerOptions;
        private TenaxConfigurationDTO _tenaxConfiguration;

        public TenaxApi(
            HttpClient client,
            ILogger<TenaxApi> logger,
            IConfigurationService configurationService
        )
        {
            _client = client;
            _logger = logger;
            _configurationService = configurationService;


            _jsonSerializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };

            _tenaxConfiguration = _configurationService.GetTenaxConfiguration().GetAwaiter().GetResult();
            _client.BaseAddress = new Uri(_tenaxConfiguration.BaseURL);
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public async Task<string> GenerateSignature(HttpRequestMessage request)
        {
            // Get the request path and query, we don't want to get scheme, host, or port, as these can
            // be affected by things like firewall/load balancer/proxy etc.
            var requestUri = request.RequestUri.PathAndQuery;
            string encodedUri = System.Web.HttpUtility.UrlEncode(requestUri).ToLower();
            _logger.LogInformation($"TenaxApi encodedUri {encodedUri}");
            var Sb = new StringBuilder();
            // Get the request body as a string
            var content = request.Content == null ? "" : await request.Content.ReadAsStringAsync();
            // hash the body using SHA256

            var enc = Encoding.UTF8;
            var result = SHA256.HashData(enc.GetBytes(content));
            foreach (byte b in result)
                Sb.Append(b.ToString("x2"));

            var bodyHash = Sb.ToString();
            // conver the hash to bytes
            var bodyHashEncodedBytes = ASCIIEncoding.ASCII.GetBytes(bodyHash);
            // convert the bytes to base64
            var bodyHashBase64 = Convert.ToBase64String(bodyHashEncodedBytes);
            _logger.LogInformation($"TenaxApi bodyHashBase64 {bodyHashBase64}");
            // get all the data together to sign it
            var data = encodedUri + request.Method + bodyHashBase64;
            _logger.LogInformation($"TenaxApi sigData {data}");
            var tenaxConfiguration = await _configurationService.GetTenaxConfiguration();
            var secretKey = tenaxConfiguration.SecretKey;
            var keyBytes = Encoding.ASCII.GetBytes(secretKey);
            var Sb2 = new StringBuilder();
            // sign the daga
            using (var hmacsha256 = new HMACSHA256(keyBytes))
            {
                byte[] hash = hmacsha256.ComputeHash(Encoding.UTF8.GetBytes(data));
                foreach (byte b in hash)
                    Sb2.Append(b.ToString("x2"));
            }

            var signature = Sb2.ToString();
            _logger.LogInformation($"TenaxApi signature {signature}");
            var apiKey = tenaxConfiguration.ApiKey;
            return apiKey + ":" + signature;
        }

        public async Task<List<TenaxTenant>> GetTenants(bool includeUsers = false)
        {
            // The URL should be absolute and not relative, otherwise the signature would not include the domain which makes it stronger and more complex
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/tenants" + (includeUsers ? "?includeUsers=true" : ""));
            return await SendAsync<List<TenaxTenant>>(httpRequestMessage);
        }

        public async Task<List<TenaxMSP>> GetMSPs(bool includeUsers = false)
        {
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/msp" + (includeUsers ? "?includeUsers=true" : ""));
            return await SendAsync<List<TenaxMSP>>(httpRequestMessage);
        }

        public async Task<TenaxTenant> CreateMSPTenant(string tenantName)
        {
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _tenaxConfiguration.BaseURL + "/master/msp");
            var requestParams = new Dictionary<string, string>
            {
                { "name", tenantName }
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<TenaxTenant>(httpRequestMessage);
        }


        public async Task<TenaxTenant> CreateTenant(string tenantName)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _tenaxConfiguration.BaseURL + "/master/tenants");
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "name", tenantName },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<TenaxTenant>(httpRequestMessage);
        }

        public async Task<TenaxResultBatch<TenaxUser>> GetMSPUsers(string mspId)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/users/" + mspId);
            return await SendAsync<TenaxResultBatch<TenaxUser>>(httpRequestMessage);
        }

        public async Task<TenaxResultBatch<TenaxUser>> GetTenantUsers(string tenantId)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users/list");
            return await SendAsync<TenaxResultBatch<TenaxUser>>(httpRequestMessage);
        }

        public async Task<List<string>> GetTenantRoles()
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/tenants/roles");
            return await SendAsync<List<string>>(httpRequestMessage);
        }

        public async Task<List<string>> GetMSPRoles()
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, _tenaxConfiguration.BaseURL + "/master/users/roles");
            return await SendAsync<List<string>>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> CreateTenantUser(string tenantId, CreateUserRequest request)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users");
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "firstName", request.FirstName },
                { "lastName", request.LastName },
                { "email", request.Email },
                { "roles", request.Roles },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> CreateMSPUser(string mspId, CreateUserRequest request)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, _tenaxConfiguration.BaseURL + "/master/users/" + mspId);
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "firstName", request.FirstName },
                { "lastName", request.LastName },
                { "email", request.Email },
                { "roles", request.Roles },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> DeactivateTenantUser(string tenantId, string userId)
        {
            HttpRequestMessage httpRequestMessage =
                new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users/" + userId + "/deActivate");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> ActivateTenantUser(string tenantId, string userId)
        {
            HttpRequestMessage httpRequestMessage =
                new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users/" + userId + "/activate");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> DeactivateMSPUser(string mspId, string userId)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/users/" + mspId + "/deactivate/" + userId);
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> ActivateMSPUser(string mspId, string userId)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/users/" + mspId + "/activate/" + userId);
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> ReinviteTenantUser(string tenantId, string userId)
        {
            HttpRequestMessage httpRequestMessage =
                new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users/" + userId + "/reInvite");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> ReinviteMSPUser(string mspId, string userId)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/users/" + mspId + "/reinvite/" + userId);
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> UpdateMSPUser(string mspId, string userId, EditUserRequest request)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/users/" + mspId + "/" + userId);
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "firstName", request.FirstName },
                { "lastName", request.LastName },
                { "roles", request.Roles },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        public async Task<HttpResponseMessage> UpdateTenantUser(string tenantId, string userId, EditUserRequest request)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Patch, _tenaxConfiguration.BaseURL + "/master/tenants/" + tenantId + "/users/" + userId);
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "firstName", request.FirstName },
                { "lastName", request.LastName },
                { "roles", request.Roles },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<HttpResponseMessage>(httpRequestMessage);
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            T res = new T();

            if (response.Content == null)
            {
                return res;
            }

            var body = await response.Content.ReadAsStreamAsync();
            return await JsonSerializer.DeserializeAsync<T>(body, _jsonSerializerOptions);
        }

        private async Task<T> SendAsync<T>(HttpRequestMessage request) where T : new()
        {
            HttpResponseMessage response = null;
            try
            {
                var signature = await GenerateSignature(request);
                request.Headers.Add("SIGNATURE", signature);
                response = await _client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await GetDeserializedResponse<T>(response);
            }
            catch (HttpRequestException ex)
            {
                var message = "";
                if (response?.Content != null)
                {
                    var responseContent = await response.Content.ReadAsStreamAsync();
                    var deserializedResponseContent = await JsonSerializer.DeserializeAsync<object>(responseContent);
                    message = deserializedResponseContent.ToString();
                }

                _logger.LogError("Tenax API - " + message, ex);
                throw;
            }
        }
    }
}