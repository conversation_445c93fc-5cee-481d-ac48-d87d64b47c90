using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class PortApi : TypeApi
{
    public PortApi(OntapApi client) : base(client)
    {
    }

    public async Task<List<Port>> GetPorts()
    {
        return await Client.GetResources<Port>(UrlBuilder.ListUrl("/network/ethernet/ports"));
    }

    public async Task<List<Port>> GetPorts(string baseName)
    {
        return await Client.GetResources<Port>(UrlBuilder.ListUrl("/network/ethernet/ports").AddQueryParam("name", baseName));
    }

    public async Task<List<Port>> GetPorts(int vlan, string basePortName)
    {
        return await Client.GetResources<Port>(UrlBuilder.ListUrl("/network/ethernet/ports")
            .AddQueryParam("vlan.tag", vlan.ToString())
            .AddQueryParam("vlan.base_port.name", basePortName));
    }

    public async Task<Port> CreateVlanPort(Port basePort, int vlanTag, Guid broadcastDomainUuid)
    {
        var body = new Port(basePort, vlanTag, broadcastDomainUuid);
        return await Client.CreateResource(new UrlBuilder("/network/ethernet/ports"), body);
    }

    public async Task<Job> DeletePort(Guid uuid)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/network/ethernet/ports", uuid));
    }
}