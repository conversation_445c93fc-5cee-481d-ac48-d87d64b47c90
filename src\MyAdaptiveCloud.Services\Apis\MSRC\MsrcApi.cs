using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Services.Apis.Msrc.Model;
using MyAdaptiveCloud.Services.DTOs.Msrc;
using MyAdaptiveCloud.Services.Services;

/*
 * Microsoft Security Response Center API
 */
namespace MyAdaptiveCloud.Services.Apis.Msrc
{
    public interface IMsrcApi
    {
        Task<CvrfDocument> GetDocument(string documentId);
        Task<List<CvrfUpdate>> GetUpdates();
    }

    public class MsrcApi : IMsrcApi
    {
        private readonly HttpClient _client;
        private readonly IConfigurationService _configurationService;
        private readonly ILogger _logger;
        private readonly JsonSerializerOptions _jsonSerializerOptions;
        private MsrcConfigurationDTO _msrcConfiguration;

        public MsrcApi(
            HttpClient client,
            ILogger<MsrcApi> logger,
            IConfigurationService configurationService
        )
        {
            _client = client;
            _logger = logger;
            _configurationService = configurationService;


            _jsonSerializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };

            _msrcConfiguration = _configurationService.GetMsrcConfiguration().GetAwaiter().GetResult();
            _client.BaseAddress = new Uri(_msrcConfiguration.BaseURL);
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        private async Task<TResponse> GetAsync<TResponse>(string endpoint, Dictionary<string, string> requestParams = null)
        {
            var baseUrl = $"{_msrcConfiguration.BaseURL}/{endpoint}";
            var requestUrl = requestParams != null && requestParams.Count > 0
                ? QueryHelpers.AddQueryString(baseUrl, requestParams)
                : QueryHelpers.AddQueryString(baseUrl, new Dictionary<string, string>());
            var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            var response = await _client.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseStream = await response.Content.ReadAsStreamAsync();

            if (responseStream == null || responseStream.Length == 0)
            {
                return default; // or throw an exception if you expect a response
            }

            var parsedResponse = await JsonSerializer.DeserializeAsync<TResponse>(responseStream, _jsonSerializerOptions);
            return parsedResponse;
        }

        public async Task<List<CvrfUpdate>> GetUpdates()
        {
            var updateResponse = await GetAsync<CvrfUpdateResponse>("updates");
            return updateResponse?.Value;
        }

        public async Task<CvrfDocument> GetDocument(string documentId)
        {
            return await GetAsync<CvrfDocument>($"cvrf/{documentId}");
        }
    }
}