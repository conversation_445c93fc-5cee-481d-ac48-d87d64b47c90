-- liquibase formatted sql

-- changeset voviedo:d0ee2cdf-9379-4b8c-89d0-5a9193de083b context:main
ALTER TABLE IF EXISTS device_alert ADD COLUMN organization_device_alert_threshold_id INT NULL DEFAULT NULL AFTER device_alert_threshold_override_id; 

-- changeset voviedo:g9eab2bf-476f-48b8-80e5-640b7de843cb context:main
ALTER TABLE IF EXISTS device_alert
ADD CONSTRAINT `fk_org_dev_al_th_id_org_dev_al_th_org_dev_al_th_id` FOREIGN KEY IF NOT EXISTS (`organization_device_alert_threshold_id`) REFERENCES organization_device_alert_threshold (organization_device_alert_threshold_id) ON UPDATE NO ACTION ON DELETE NO ACTION;