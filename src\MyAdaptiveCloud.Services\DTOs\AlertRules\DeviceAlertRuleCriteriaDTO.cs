﻿namespace MyAdaptiveCloud.Services.DTOs.AlertRules
{
    public class DeviceAlertRuleCriteriaDTO
    {
        public int DeviceAlertRuleCriteriaId { get; set; }

        public int DeviceAlertRuleId { get; set; }

        public int DeviceAlertTypeId { get; set; }

        public int DeviceAlertThresholdLevelId { get; set; }

        public int DeviceAlertRuleComparerId { get; set; }

        public bool Enabled { get; set; }

        public string Description { get; set; }

        public int DisplayOrder { get; set; }

        public DateTimeOffset? CreatedOn { get; set; }

        public int? CreatedBy { get; set; }


    }
}
