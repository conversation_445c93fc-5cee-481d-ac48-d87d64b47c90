# Copy this file to the filename liquibase.local.properties and set the following values. The resulting file should NOT be committed to the repo.
# Enter the username for your Target database.
liquibase.command.username: yourdbuser

# Enter the password for your Target database.
liquibase.command.password: yourdbpass

# Enter URL for the source database - Use these to temporarily change your reference db
#liquibase.command.referenceUrl: offline:mariadb?snapshot=baseline.json
#liquibase.command.referenceUrl: offline:mariadb?snapshot=baselinev.1.3.json

# Enter the username for your source database
#liquibase.command.referenceUsername: dbuser

# Enter the password for your source database
#liquibase.command.referencePassword: letmein
