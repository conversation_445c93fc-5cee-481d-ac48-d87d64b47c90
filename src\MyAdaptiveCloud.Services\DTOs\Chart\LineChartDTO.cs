namespace MyAdaptiveCloud.Services.DTOs.Chart
{
    public class LineChartDTO
    {
        public string Label { get; set; }

        public DataPointDTO[] Data { get; set; }
        
        public DataPointDTO[] DataGBFree { get; set; }
        
        public LineChartDTO(int count)
        {
            Data = new DataPointDTO[count];
            DataGBFree = new DataPointDTO[count];

            for (int idx = 0; idx < count; idx++)
            {
                Data[idx] = new DataPointDTO();
                DataGBFree[idx] = new DataPointDTO();
            }
        }
    }
}