﻿namespace MyAdaptiveCloud.Services.DTOs.CloudInfra
{
    public class DomainAdminPanelUsersResponseDTO
    {
        public DomainAdminPanelUsersResponseDTO()
        {
            AdminUsers = new List<CloudInfraUserListModel>();
            OrgUsers = new List<CloudInfraUserListModel>();
        }
        public List<CloudInfraUserListModel> AdminUsers { get; set; }
        public List<CloudInfraUserListModel> OrgUsers { get; set; }
        public Guid? DomainAdminAccountId { get; set; }

        public Guid? DomainAdminDomainId { get; set; }

        public int OrganizationId { get; set; }

    }
}
