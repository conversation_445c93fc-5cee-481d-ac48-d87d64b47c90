using AutoMapper;
using MyAdaptiveCloud.Data.Agent;
using MyAdaptiveCloud.Services.DTOs.Agent;

namespace MyAdaptiveCloud.Services.AutoMapper.Agents
{
    public class AgentMapperProfile : Profile
    {
        public AgentMapperProfile()
        {
            CreateMap<AgentDTO, Agent>()
                .ForMember(dest => dest.AgentUuid, opt => opt.Ignore())
                .ForMember(dest => dest.Hostname, opt => opt.Ignore())
                .ForMember(dest => dest.Ip, opt => opt.Ignore())
                .ForMember(dest => dest.OperatingSystem, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedTs, opt => opt.Ignore())
                .ForMember(dest => dest.HeartbeatTs, opt => opt.Ignore())
                .ForMember(dest => dest.AgentSvcId, opt => opt.Ignore())
                .ForMember(dest => dest.WatchdogSvcId, opt => opt.Ignore())
                .ForMember(dest => dest.AgentService, opt => opt.Ignore())
                .ForMember(dest => dest.WatchdogService, opt => opt.Ignore())
                .ForMember(dest => dest.HealthStats, opt => opt.Ignore())
                .ForMember(dest => dest.PubKey, opt => opt.Ignore())
                .ForMember(dest => dest.AgentId, opt => opt.Ignore())
                .ForMember(dest => dest.MarkedForDeletionOn, opt => opt.Ignore())
                .ForMember(dest => dest.LastHealthStatId, opt => opt.Ignore())
                .ForMember(dest => dest.PubKeyExp, opt => opt.Ignore())
                .ForMember(dest => dest.IsMarkedReRegister, opt => opt.Ignore())
                .ForMember(dest => dest.IsMarkedUninstall, opt => opt.Ignore())
                .ForMember(dest => dest.CloudStackVM, opt => opt.Ignore())
                .ForMember(dest => dest.LastLogonTs, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.Ignore())
                .ForMember(dest => dest.LastLogonUsername, opt => opt.Ignore())
                .ForMember(dest => dest.RequiredAgentReleaseTagId, opt => opt.Ignore())
                .ForMember(dest => dest.RequiredWatchdogReleaseTagId, opt => opt.Ignore());


            CreateMap<Agent, AgentDTO>()
                .ForMember(dest => dest.OrgId, opt => opt.MapFrom(src => src.OrgId))
                .ForMember(dest => dest.OrganizationName, opt => opt.Ignore())
                .ForMember(dest => dest.Hostname, opt => opt.MapFrom(src => src.Hostname))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.FolderId, opt => opt.Ignore())
                .ForMember(dest => dest.Path, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => $"{src.Hostname} {src.AgentId}"));

            CreateMap<HealthStatDisk, HealthStatDiskDTO>()
                .ForMember(dest => dest.WarningThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.ErrorThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.CriticalThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.InheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.AlertThresholdTypeName, opt => opt.Ignore());

            CreateMap<HealthStatDiskDTO, HealthStatDisk>()
                .ForMember(dest => dest.HsId, opt => opt.Ignore())
                .ForMember(dest => dest.SizeTotal, opt => opt.Ignore())
                .ForMember(dest => dest.SizeUsed, opt => opt.Ignore())
                .ForMember(dest => dest.HealthStat, opt => opt.Ignore());

            CreateMap<HealthStat, HealthStatDTO>()
                .ForMember(dest => dest.Disks, opt => opt.MapFrom(src => src.HealthStatDisks));

            CreateMap<HealthStatDTO, HealthStat>()
                .ForMember(dest => dest.Agent, opt => opt.Ignore())
                .ForMember(dest => dest.AgentId, opt => opt.Ignore())
                .ForMember(dest => dest.CpuUsage, opt => opt.Ignore())
                .ForMember(dest => dest.RamTotal, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsed, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsage, opt => opt.Ignore())
                .ForMember(dest => dest.HealthStatDisks, opt => opt.Ignore());

            CreateMap<AgentSummary, AgentSummaryDTO>()
                .ForMember(dest => dest.AgentId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.UptimeForDisplayDaysHoursMinutes, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsageWarningThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.CPUUsageWarningThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.HeartbeatWarningThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsageErrorThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.CPUUsageErrorThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.HeartbeatErrorThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsageCriticalThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.CPUUsageCriticalThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.HeartbeatCriticalThreshold, opt => opt.Ignore())
                .ForMember(dest => dest.HeartbeatInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.CPUUsageInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsageInheritanceType, opt => opt.Ignore())
                .ForMember(dest => dest.RamUsageAlertThresholdTypeName, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.Ignore())
                .ForMember(dest => dest.Disks, opt => opt.Ignore())
                .ForMember(dest => dest.ActiveAlerts, opt => opt.Ignore())
                .ForMember(dest => dest.InScheduleDowntime, opt => opt.Ignore())
                .ForMember(dest => dest.ScheduleDowntimeEndDate, opt => opt.Ignore())
                .ForMember(dest => dest.AcknowledgedBy, opt => opt.Ignore())
                .ForMember(dest => dest.AcknowledgedDate, opt => opt.Ignore())
                .ForMember(dest => dest.Organization, opt => opt.Ignore())
                .ForMember(dest => dest.CanRemoteDesktop, opt => opt.Ignore())
                .ForMember(dest => dest.CanRemoteCommands, opt => opt.Ignore())
                .ForMember(dest => dest.FolderIdsPathFromOrganization, opt => opt.Ignore())
                .ForMember(dest => dest.CloudStackVM, opt => opt.MapFrom(src => src.CloudStackVM))
                .ForMember(dest => dest.LastLogonUsername, opt => opt.MapFrom(src => src.LastLogonUsername));

            CreateMap<SoftwareInventorySummaryQuery, SoftwareInventoryApplicationDTO>()
                .ForMember(dest => dest.InstalledOn, opt => opt.MapFrom(r => r.InstalledOn))
                .ForMember(dest => dest.Version, opt => opt.MapFrom(r => r.Version))
                .ForMember(dest => dest.HostName, opt => opt.MapFrom(r => r.HostName))
                .ForMember(dest => dest.HasUninstall, opt => opt.MapFrom(r => r.HasUninstall))
                .ForMember(dest => dest.HasUninstall, opt => opt.MapFrom(r => r.HasUninstall))
                .ForMember(dest => dest.SoftwareInventoryStatus, opt => opt.MapFrom(r => r.SoftwareInventoryStatus))
                .ForMember(dest => dest.CanViewUninstallSoftwareInventory, opt => opt.MapFrom(r => r.CanViewUninstallSoftwareInventory))
                .ForMember(dest => dest.AgentId, opt => opt.MapFrom(r => r.AgentId));
        }
    }
}