﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.ApiUser;

namespace MyAdaptiveCloud.Services.AutoMapper.ApiUsers
{
    public class ApiUserMapperProfile : Profile
    {
        public ApiUserMapperProfile()
        {
            CreateMap<User, ApiUserWithRolesDTO>()
                .ForMember(dest => dest.ApiUserId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PersonId, opt => opt.MapFrom(src => src.PersonId))
                .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.Person.CreatedDate))
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.Person.CreatedByPerson))
                .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => src.UserRoles.Select(src => src.Role.Name).OrderBy(r => r)))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Person.LastName))
                .ForMember(dest => dest.CanDeleteUsers, opt => opt.Ignore())
                .ForMember(dest => dest.CanEditUsers, opt => opt.Ignore());

            CreateMap<Person, ApiUserDTO>()
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedByPerson))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.LastName))
                .ForMember(dest => dest.PersonId, opt => opt.MapFrom(src => src.PersonId))
                .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => src.CreatedDate))
                .ForMember(dest => dest.CanDeleteUsers, opt => opt.Ignore())
                .ForMember(dest => dest.CanViewUsers, opt => opt.Ignore());
        }
    }
}