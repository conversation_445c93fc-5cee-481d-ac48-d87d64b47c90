namespace MyAdaptiveCloud.Services.Authentication
{
    public class UserContext
    {
        public string Email { get; set; }

        public string Name { get; set; }

        public int UserId { get; set; }

        public int? OrganizationId { get; set; }

        public string OrganizationName { get; set; }

        public bool AllowSubOrg { get; set; }

        public bool AllowWhiteLabel { get; set; }

        public WhiteLabelContext WhiteLabel { get; set; }

        public bool IsRegistered { get; set; }

        public bool IsApproved { get; set; }

        public bool IsPartner { get; set; }

        public byte[] Permissions { get; internal set; }

        public List<string> FeatureFlags { get; set; }
    }
}