namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public enum IpType
{
    ipv4,
    ipv6
}

public class Route : OntapObject
{
    public Destination Destination { get; set; }
    public string Gateway { get; set; }

    public Svm Svm { get; set; }

    public Route()
    {
    }

    public Route(string gateway, Guid svm)
    {
        Gateway = gateway;
        Svm = new Svm(svm);
    }

    public override string ToString()
    {
        return $"Route: {Uuid} {Gateway} Svm: {Svm}";
    }
}

public class Destination
{
    public string Address { get; set; }
    public string Netmask { get; set; }
    public IpType Family { get; set; }
}