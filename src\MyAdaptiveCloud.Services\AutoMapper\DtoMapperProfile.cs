using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Policies;
using MyAdaptiveCloud.Services.Apis.CloudStack.Model;
using MyAdaptiveCloud.Services.AutoMapper.Extensions;
using MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.CloudInfra;
using MyAdaptiveCloud.Services.DTOs.Policy;
using Configuration = MyAdaptiveCloud.Services.Apis.ConnectWise.Model.Configuration;

namespace MyAdaptiveCloud.Services.AutoMapper
{
    public class DtoMapperProfile : Profile
    {
        public DtoMapperProfile()
        {
            CreateMap<AccountResponse, Account>();

            CreateMap<Configuration, ConfigurationModel>();

            CreateMap<UpdateCategoryAutoApprovalPolicyDTO, UpdateCategoryAutoApprovalPolicy>();
            CreateMap<UpdateCategoryAutoApprovalPolicy, UpdateCategoryAutoApprovalPolicyDTO>();

            CreateMap<Policy, DTOs.Policy.PolicyDTO>()
                .ForMember(dest => dest.CanView, opt => opt.Ignore())
                .ForMember(dest => dest.CanCreate, opt => opt.Ignore())
                .ForMember(dest => dest.CanEdit, opt => opt.Ignore())
                .ForMember(dest => dest.CanDelete, opt => opt.Ignore());

            CreateMap<DTOs.Policy.PolicyDTO, Policy>()
                .ForMember(dest => dest.Organization, opt => opt.Ignore())
                .ForMember(dest => dest.Schedule, opt => opt.Ignore())
                .ForMember(dest => dest.UpdateCategoriesAutoApproval, opt => opt.Ignore())
                .ForMember(dest => dest.ReleaseTagName, opt => opt.Ignore());

            CreateMap<Policy, AppliedPolicyDTO>()
                .ForMember(dest => dest.InheritedFrom, opt => opt.Ignore())
                .ForMember(dest => dest.InheritedFromFolderId, opt => opt.Ignore())
                .ForMember(dest => dest.InheritedFromOrganizationId, opt => opt.Ignore())
                .IgnoreViewModelUserActions();

            CreateMap<AccountResponse, AccountLimitDTO>();

            CreateMap<Domain, AccountLimitDTO>();
        }
    }
}