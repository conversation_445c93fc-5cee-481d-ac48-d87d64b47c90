﻿using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Services.Apis.Acronis.Enums;
using MyAdaptiveCloud.Services.Apis.Acronis.Model;
using MyAdaptiveCloud.Services.Exceptions;
using MyAdaptiveCloud.Services.Services;
using System.Globalization;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.Acronis
{
    public class AcronisApi : IAcronisApi
    {
        private readonly HttpClient _client;
        private readonly ILogger _logger;
        private readonly string external_idp = "11111111-1111-1111-1111-111111111111"; //Default value for it to allow us to use the activation-email endpoint
        private IAcronisApi.InvalidCredentials _invalidCredentialsDelegate;

        private readonly string _baseURL;

        public AcronisApi(
            IConfigurationService configurationService,
            HttpClient client,
            ILogger<AcronisApi> logger)
        {
            _client = client;
            _logger = logger;

            var configuration = configurationService.GetDataProtectionConfiguration().GetAwaiter().GetResult();
            _baseURL = configuration.BaseURL;

            _client.BaseAddress = !string.IsNullOrEmpty(_baseURL) ? new Uri(_baseURL) : null;
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public IAcronisApi.InvalidCredentials InvalidCredentialsDelegate
        {
            set { _invalidCredentialsDelegate = value; }
        }

        private async Task<AcronisToken> GetAccessToken(IDataProtectionCredential credentials, bool clearCache = false)
        {
            if (credentials.token == null)
            {
                using (var request = new HttpRequestMessage(HttpMethod.Post, _baseURL + "/idp/token"))
                {
                    Dictionary<string, string> requestParams = new Dictionary<string, string>
                    {
                        { "grant_type", "client_credentials" },
                    };
                    request.Headers.Authorization =
                        new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials.ClientId + ":" + credentials.SecretKey)));
                    request.Content = new FormUrlEncodedContent(requestParams);
                    var response = await _client.SendAsync(request);
                    AcronisToken tokenResponse = null;
                    if (response.IsSuccessStatusCode)
                    {
                        tokenResponse = await GetDeserializedResponse<AcronisToken>(response);
                    }
                    else
                    {
                        if (response.StatusCode == HttpStatusCode.Unauthorized || response.StatusCode == HttpStatusCode.Gone)
                        {
                            await DeleteCurrentTenantCredentials(credentials);
                            throw new BadRequestException("Data Protection Api Client has been deleted.");
                        }
                    }

                    return tokenResponse;
                }
            }

            ;

            return credentials.token;
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            T res = new T();

            if (response.Content == null)
            {
                return res;
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
            };
            return await JsonSerializer.DeserializeAsync<T>(await response.Content.ReadAsStreamAsync(), options);
        }

        private async Task<T> SendAsync<T>(HttpRequestMessage request, IDataProtectionCredential credentials) where T : new()
        {
            var response = await SendAsync(request, credentials);

            return await GetDeserializedResponse<T>(response);
        }

        private async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, IDataProtectionCredential credentials)
        {
            try
            {
                credentials.token = await GetAccessToken(credentials);
                if (credentials.token == null)
                {
                    // Failed to get an access token
                    // TODO How do we construct a generic 401 response here?
                    throw new HttpRequestException("Acronis Api Failed to get an access token", null, HttpStatusCode.Unauthorized);
                }

                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", credentials.token.Token);
                var response = await _client.SendAsync(request);
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    _logger.LogInformation("AcronisApi - bad ApiClient, deleting it.");
                    await DeleteCurrentTenantCredentials(credentials);
                    throw new BadRequestException("Data Protection Api Client has been deleted.");
                }

                response.EnsureSuccessStatusCode();
                return response;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Acronis API error");
                throw;
            }
        }

        public async Task<AcronisClient> GetClient(IDataProtectionCredential credentials)
        {
            AcronisClient result = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseURL + "/clients/" + credentials.ClientId)))
            {
                result = await SendAsync<AcronisClient>(request, credentials);
            }

            return result;
        }

        public async Task<AcronisTenant> GetTenant(string tenantId, IDataProtectionCredential credentials)
        {
            AcronisTenant result = null;
            using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseURL + "/tenants/" + tenantId)))
            {
                result = await SendAsync<AcronisTenant>(request, credentials);
            }

            return result;
        }

        public async Task<AcronisTenant> CreateTenant(string organizationName, bool isPartner, string parentTenantId, string companyShortName,
            IDataProtectionCredential credentials)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseURL + "/tenants")))
            {
                var requestParams = new Dictionary<string, object>
                {
                    { "name", organizationName },
                    { "parent_id", parentTenantId },
                    { "kind", isPartner ? "partner" : "customer" },
                    { "enabled", true },
                    { "ancestral_access", true },
                    { "default_idp_id", external_idp }
                };

                if (!string.IsNullOrEmpty(companyShortName))
                {
                    requestParams.Add("internal_tag", companyShortName);
                    requestParams.Add("customer_id", companyShortName);
                }

                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                return await SendAsync<AcronisTenant>(request, credentials);
            }
        }

        public async Task DeleteTenant(string tenantId, IDataProtectionCredential credentials)
        {
            var tenant = await GetTenant(tenantId, credentials);
            tenant = await UpdateTenant(tenant.Id, null, null, null, false, tenant.Version, null, credentials);

            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "version", tenant.Version.ToString() }
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId, requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Delete, relativeUriString);
            await SendAsync(request, credentials);
        }

        public async Task<AcronisTenant> UpdateTenant(string tenantId, string organizationName, string parentTenantId, string companyShortName, bool? enabled, int version,
            bool? ancestralAccess, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/tenants/" + tenantId));
            var requestParams = new Dictionary<string, object>
            {
                { "version", version }
            };
            if (enabled != null)
            {
                requestParams.Add("enabled", enabled);
            }

            if (ancestralAccess != null)
            {
                requestParams.Add("ancestral_access", ancestralAccess);
            }

            if (!string.IsNullOrEmpty(parentTenantId))
            {
                requestParams.Add("parent_id", parentTenantId);
            }

            if (!string.IsNullOrEmpty(companyShortName))
            {
                requestParams.Add("customer_id", companyShortName);
            }

            if (!string.IsNullOrEmpty(organizationName))
            {
                requestParams.Add("name", organizationName);
            }

            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<AcronisTenant>(request, credentials);
        }

        public async Task<AcronisTenant> ChangeAncestralAccess(string tenantId, int version, bool ancestralAccess, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/tenants/" + tenantId));
            var requestParams = new Dictionary<string, object>
            {
                { "version", version },
                { "ancestral_access", ancestralAccess },
            };
            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<AcronisTenant>(request, credentials);
        }

        public async Task<AcronisResultBatch<AcronisApplication>> GetApplications(IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseURL + "/applications"));
            return await SendAsync<AcronisResultBatch<AcronisApplication>>(request, credentials);
        }

        public async Task<AcronisApplication> GetApplicationById(string applicationId, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseURL + "/applications/" + applicationId));
            return await SendAsync<AcronisApplication>(request, credentials);
        }


        public async Task EnableApplication(string applicationId, string tenantId, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseURL + "/applications/" + applicationId + "/bindings/tenants/" + tenantId));
            await SendAsync(request, credentials);
        }

        public async Task EnableOfferingItems<T>(string applicationId, string tenantId, List<T> items, List<Guid> infraIds, IDataProtectionCredential credentials) where T : Enum
        {
            var offeringItems = await GetOfferingItems(tenantId, credentials);
            var offeringItemsJSON = new List<Dictionary<string, object>>();
            var quota = new Dictionary<string, object>
            {
                { "overage", null },
                { "value", null },
                { "version", 0 }
            };

            foreach (var setting in items)
            {
                var settingStatus = EnumHelper.GetDescription(setting);
                var settingItems =
                    offeringItems.Items.FindAll(x => x.Name == settingStatus); //FindAll because there are multiple Backup Locations with the same Name, different InfraId
                if (infraIds != null)
                {
                    var infras = infraIds.Select(x => x.ToString()).ToList();
                    settingItems = settingItems.Where(x => infras.Contains(x.InfraId) || x.InfraId == null).ToList();
                }

                foreach (var settingData in settingItems)
                {
                    var body = new Dictionary<string, object>
                    {
                        { "application_id", applicationId },
                        { "name", settingData.Name },
                        { "status", 1 },
                        { "quota", quota },
                        { "infra_id", settingData.InfraId }
                    };
                    offeringItemsJSON.Add(body);
                }
            }

            using (var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/tenants/" + tenantId + "/offering_items")))
            {
                var requestParams = new Dictionary<string, object>
                {
                    { "check_usage", false },
                    { "offering_items", offeringItemsJSON }
                };

                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                await SendAsync<AcronisResultBatch<AcronisOfferingItem>>(request, credentials);
            }
        }

        public async Task<AcronisResultBatch<AcronisOfferingItem>> GetOfferingItems(string tenantId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "for_ui", "false" },
                { "edition", "*" }
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId + "/offering_items", requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisResultBatch<AcronisOfferingItem>>(request, credentials);
        }

        public async Task<AcronisUser> CreateUser(string tenantId, string email, string firstname, string lastname, List<string> types, IDataProtectionCredential credentials)
        {
            using (var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseURL + "/users")))
            {
                var contact = new Dictionary<string, object>
                {
                    { "email", email },
                    { "firstname", firstname },
                    { "lastname", lastname },
                };

                if (types?.Count > 0)
                {
                    contact.Add("types", types);
                }

                var requestParams = new Dictionary<string, object>
                {
                    { "tenant_id", tenantId },
                    { "login", email },
                    { "enabled", true },
                    { "contact", contact },
                    { "idp_id ", external_idp },
                };

                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                var user = await SendAsync<AcronisUser>(request, credentials);

                return user;
            }
        }

        public async Task<AcronisResultBatch<AcronisAccessPolicy>> GetUserAccessPolicies(string acronisUserId, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Get, new Uri(_baseURL + "/users/" + acronisUserId + "/access_policies"));
            return await SendAsync<AcronisResultBatch<AcronisAccessPolicy>>(request, credentials);
        }

        public async Task<AcronisResultBatch<AcronisAccessPolicy>> UpdateUserAccessPolicies(string acronisUserId, List<Dictionary<string, object>> policies,
            IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/users/" + acronisUserId + "/access_policies"));
            {
                var requestParams = new Dictionary<string, object>
                {
                    { "timestamp", DateTime.UtcNow.ToString("o", CultureInfo.InvariantCulture) },
                    { "items", policies },
                };

                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                return await SendAsync<AcronisResultBatch<AcronisAccessPolicy>>(request, credentials);
            }
        }

        public async Task SendActivationEmail(string acronisUserId, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseURL + "/users/" + acronisUserId + "/send-activation-email"));
            {
                var requestParams = new Dictionary<string, object> { }; // Requires an empty body 

                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                await SendAsync(request, credentials);
            }
        }

        public async Task<AcronisResultBatch<AcronisTenant>> GetTenantChildren(string tenantId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "include_details", "true" },
                { "allow_deleted", "false" },
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId + "/children", requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisResultBatch<AcronisTenant>>(request, credentials);
        }

        public async Task<AcronisResultBatch<string>> GetTenantUsers(string tenantId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "allow_deleted", "false" },
                { "include_grand_children", "false" },
                { "include_hidden", "false" },
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId + "/users", requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisResultBatch<string>>(request, credentials);
        }

        public async Task<AcronisUser> GetUser(string userId, IDataProtectionCredential credentials)
        {
            try
            {
                Dictionary<string, string> requestParams = new Dictionary<string, string>
                {
                    { "allow_deleted", "false" }
                };
                string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/users/" + userId, requestParams);

                using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
                return await SendAsync<AcronisUser>(request, credentials);
            }
            catch (HttpRequestException ex)
            {
                if (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    throw new NotFoundException(userId, "The user does not exist in Acronis.");
                }

                throw;
            }
        }

        public async Task<AcronisTenantLocations> GetTenantAvailableLocations(string tenantId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string> { };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId + "/locations", requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisTenantLocations>(request, credentials);
        }

        public async Task<AcronisLocationInfrastructures> GetLocationInfras(string locationId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string> { };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/locations/" + locationId + "/infra", requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisLocationInfrastructures>(request, credentials);
        }

        public async Task<AcronisLocation> GetLocation(string locationId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string> { };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/locations/" + locationId, requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisLocation>(request, credentials);
        }

        public async Task<AcronisInfrastructure> GetInfrastructureInfo(string infraId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string> { };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/infra/" + infraId, requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisInfrastructure>(request, credentials);
        }

        public async Task<AcronisResultBatch<AcronisInfrastructure>> GetAvailableInfrastructures(string uuids, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "uuids", uuids }
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/infra", requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisResultBatch<AcronisInfrastructure>>(request, credentials);
        }

        public async Task<AcronisTenantPricing> GetTenantPricing(string tenantId, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string> { };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/tenants/" + tenantId + "/pricing", requestParams);
            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return await SendAsync<AcronisTenantPricing>(request, credentials);
        }

        public async Task UpdatePricingMode(string tenantId, AcronisPricingMode pricingMode, long version, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/tenants/" + tenantId + "/pricing"));
            {
                var requestParams = new Dictionary<string, object>
                {
                    { "mode", EnumHelper.GetDescription(pricingMode) },
                    { "version", version }
                };
                var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
                request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
                await SendAsync(request, credentials);
            }
        }

        public async Task<bool> IsValidLogin(string login, IDataProtectionCredential credentials)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "username", login }
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/users/check_login", requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Get, relativeUriString);
            return (await SendAsync(request, credentials)).IsSuccessStatusCode;
        }

        public async Task<AcronisUser> UpdateUser(string userId, bool enabled, string email, string firstname, string lastname, int version, IDataProtectionCredential credentials)
        {
            var contact = new Dictionary<string, object>
            {
                { "email", email },
                { "firstname", firstname },
                { "lastname", lastname },
            };

            using var request = new HttpRequestMessage(HttpMethod.Put, new Uri(_baseURL + "/users/" + userId));
            var requestParams = new Dictionary<string, object>
            {
                { "version", version },
                { "enabled", enabled },
                { "contact", contact },
            };

            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<AcronisUser>(request, credentials);
        }

        public async Task DeleteUser(string userId, IDataProtectionCredential credentials)
        {
            var user = await GetUser(userId, credentials);
            user = await UpdateUser(userId, false, user.Contact.Email, user.Contact.Firstname, user.Contact.Lastname, user.Version,
                credentials); // Version gets updated on this request

            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "version", user.Version.ToString() }
            };
            string relativeUriString = QueryHelpers.AddQueryString(_baseURL + "/users/" + userId, requestParams);

            using var request = new HttpRequestMessage(HttpMethod.Delete, relativeUriString);
            await SendAsync(request, credentials);
        }

        public async Task<AcronisClient> CreateClient(string tenantId, string organizationName, IDataProtectionCredential credentials)
        {
            using var request = new HttpRequestMessage(HttpMethod.Post, new Uri(_baseURL + "/clients"));

            var data = new Dictionary<string, object>
            {
                { "client_name", organizationName },
            };

            var requestParams = new Dictionary<string, object>
            {
                { "type", "api_client" },
                { "tenant_id", tenantId },
                { "token_endpoint_auth_method", "client_secret_basic" },
                { "data", data },
            };

            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            request.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
            return await SendAsync<AcronisClient>(request, credentials);
        }

        private async Task DeleteCurrentTenantCredentials(IDataProtectionCredential creds)
        {
            if (creds.OrganizationId != Constants.RootOrganizationId && creds.OrganizationId != null)
            {
                await _invalidCredentialsDelegate(creds);
            }
        }
    }
}