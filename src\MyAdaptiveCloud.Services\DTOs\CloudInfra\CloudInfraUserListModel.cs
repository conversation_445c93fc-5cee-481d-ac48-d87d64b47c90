﻿using MyAdaptiveCloud.Core.Common;

namespace MyAdaptiveCloud.Services.DTOs.CloudInfra
{
    public class CloudInfraUserListModel : BasePerson
    {
        public int? UserId { get; set; }
        public Guid? CloudInfraUserId { get; set; }
        public string UserName { get; set; }
        public string RoleName { get; set; }
        public string OrganizationName { get; set; }
        public int? OrganizationId { get; set; }
        public bool IsMappedToCloudInfraUser { get; set; }
        public Guid? CloudInfraAccountId { get; set; }
        public string DomainName { get; set; }
        public string DomainId { get; set; }
        public string AccountName { get; set; }
        public bool IsLocalUser { get; set; }
    }
}
