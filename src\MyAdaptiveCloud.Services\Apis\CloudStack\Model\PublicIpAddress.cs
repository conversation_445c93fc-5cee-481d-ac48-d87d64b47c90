﻿using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class ListPublicIPAddressesResponse : CloudStackListResponse<PublicIpAddress>
    {
        [JsonPropertyName("publicipaddress")]
        public override List<PublicIpAddress> Items { get; set; } = new List<PublicIpAddress>();
    }

    public class PublicIpAddress
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("ipaddress")]
        public string IpAddress { get; set; }

        [JsonPropertyName("account")]
        public string Account { get; set; }

        [JsonPropertyName("domainid")]
        public Guid DomainId { get; set; }

        [JsonPropertyName("domain")]
        public string Domain { get; set; }
    }
}