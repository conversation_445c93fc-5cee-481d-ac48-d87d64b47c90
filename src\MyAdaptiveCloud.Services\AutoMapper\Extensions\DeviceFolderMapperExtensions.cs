﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Policies;
using MyAdaptiveCloud.Services.DTOs.DeviceFolder;

namespace MyAdaptiveCloud.Services.AutoMapper.Extensions
{
    public static class DeviceFolderMapperExtensions
    {
        public static DeviceFolderDTO MapToDeviceFolderDTO(this IMapper mapper, DeviceFolder folder, DeviceFolderPolicyConfiguration deviceFolderPolicy) =>
            mapper.Map<DeviceFolderDTO>(Tuple.Create(folder, deviceFolderPolicy));
    }
}