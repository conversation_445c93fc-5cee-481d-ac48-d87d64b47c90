using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public abstract class TypeApi
{
    protected readonly OntapApi Client;

    public TypeApi(OntapApi client)
    {
        Client = client;
    }
    
    public async Task<Job> GetJob(Guid job)
    {
        return await Client.GetJob(job);
    }

    public async Task<Job> WaitOnJob(Job job)
    {
        return await Client.WaitOnJob(job);
    }

    public async Task<Job> WaitOnJob(Guid job)
    {
        return await Client.WaitOnJob(job);
    }
}