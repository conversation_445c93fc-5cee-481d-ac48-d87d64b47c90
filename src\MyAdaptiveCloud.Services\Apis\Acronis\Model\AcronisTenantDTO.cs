﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisTenantDTO : IDataProtectionCredential
    {
        public Guid TenantId { get; set; }
        public Guid ParentId { get; set; }
        public List<AcronisTenantDTO> Children { get; set; }
        public string Name { get; set; }
        public string Kind { get; set; }
        public int? OrganizationId { get; set; }
        public int? ParentOrganizationId { get; set; }

        [JsonIgnore]
        public string ClientId { get; set; }

        [JsonIgnore]
        public string SecretKey { get; set; }

        [JsonIgnore]
        public AcronisToken token { get; set; }

        public List<string> Path { get; set; }
    }
}