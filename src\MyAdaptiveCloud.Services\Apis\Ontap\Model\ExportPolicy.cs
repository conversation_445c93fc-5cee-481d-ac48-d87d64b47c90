namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class ExportPolicy : OntapObject
{
    public Svm Svm { get; set; }
    public List<ExportPolicyRule> Rules { get; set; }
    public long Id { get; set; }
    
    public ExportPolicy() {}

    public ExportPolicy(string name)
    {
        Name = name;
    }

    public ExportPolicy(Guid uuid)
    {
        Uuid = uuid;
    }

    public override string ToString()
    {
        return $"Export Policy {Name} {Id}";
    }
}

public class ExportPolicyRule
{
    public ExportPolicy Policy { get; set; }
    public List<string> RoRule { get; set; }
    public List<string> RwRule { get; set; }
    public List<ExportPolicyRuleClient> Clients { get; set; }

    public ExportPolicyRule() {}

    public ExportPolicyRule(string roRule, string rwRule, string clients)
    {
        RoRule = new List<string>{roRule};
        RwRule = new List<string>{rwRule};
        Clients = new List<ExportPolicyRuleClient> { new(clients) };
    }

    public override string ToString()
    {
        return $"Export Policy Rule RO:{RoRule} RW:{RwRule} Clients: {Clients}";
    }
}

public class ExportPolicyRuleClient
{
    public string Match { get; set; }
    
    public ExportPolicyRuleClient() {}

    public ExportPolicyRuleClient(string match)
    {
        Match = match;
    }

    public override string ToString()
    {
        return $"Match: {Match}";
    }
}