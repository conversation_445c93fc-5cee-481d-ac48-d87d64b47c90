using MyAdaptiveCloud.Core.Common.UserActions;

namespace MyAdaptiveCloud.Services.DTOs.AuthenticationKeys
{
    public class AuthenticationKeyDTO
    {
        public int Id { get; set; }

        public int PersonId { get; set; }

        public string Name { get; set; }

        public string A<PERSON><PERSON>ey { get; set; }

        public bool RequireSigning { get; set; }
        // Still using this property even if its not a UserAction, it is set on the service
        public UserActionStateEnum CanEdit { get; set; }
        // Still using this property even if its not a UserAction, it is set on the service
        public UserActionStateEnum CanDelete { get; set; }

    }
}