﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Files;

namespace MyAdaptiveCloud.Services.AutoMapper.FileAdministration
{
    public class FileAdministrationMapperProfile : Profile
    {
        public FileAdministrationMapperProfile()
        {
            CreateMap<AdaptiveCloudDriveFile, FileAdministrationDTO>()
                .ForMember(dest => dest.UserEmail, opt => opt.MapFrom(o => o.Updater != null ? o.Updater.Email : o.Creator.Email))
                .ForMember(dest => dest.FileAdministrationId, opt => opt.MapFrom(o => o.FileAdministrationId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(o => o.Name))
                .ForMember(dest => dest.UploadDate, opt => opt.MapFrom(o => o.UpdatedDate ?? o.CreatedDate))
                .ForMember(dest => dest.FileSize, opt => opt.MapFrom(o => o.FileSize))
                .ForMember(dest => dest.UploadUser, opt => opt.MapFrom(o => o.Updater != null ? o.Updater.FullName : o.Creator.FullName));
        }
    }
}