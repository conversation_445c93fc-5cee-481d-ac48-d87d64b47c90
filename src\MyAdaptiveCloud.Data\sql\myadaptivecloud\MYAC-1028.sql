-- liquibase formatted sql

-- changeset voviedo:823f8906-0de5-4115-aae9-40b832bf10a2 context:main
ALTER TABLE IF EXISTS `device_alert`
	CHANGE COLUMN IF EXISTS `organizationId` `organization_id` INT(11) NOT NULL AFTER `agent_id`;

-- changeset voviedo:fc6e6f9b-87af-48b0-a586-6bc2087e45c1 context:main
CREATE OR REPLACE TABLE `device_alert_to_alert_rule` (
	`device_alert_to_alert_rule_id` INT(11) NOT NULL AUTO_INCREMENT,
	`device_alert_id` INT(11) NOT NULL,
	`device_alert_rule_id` INT(11) NOT NULL,
	`iteration` INT(11) NOT NULL,
	started_on DATETIME NOT NULL,
	`organization_id` INT(11) NOT NULL,
	PRIMARY KEY (`device_alert_to_alert_rule_id`) USING BTREE,
	CONSTRAINT `fk_dev_alert_to_alert_rule_dev_alert_id_dev_alert_dev_alert_id` FOREIGN KEY (`device_alert_id`) REFERENCES `device_alert` (`device_alert_id`) ON UPDATE NO ACTION ON DELETE NO ACTION,
	CONSTRAINT `fk_dev_al_to_al_rule_dev_al_rule_id_dev_al_rule_dev_al_rule_id` FOREIGN KEY (`device_alert_rule_id`) REFERENCES `device_alert_rule` (`device_alert_rule_id`) ON UPDATE NO ACTION ON DELETE NO ACTION, 
	CONSTRAINT `fk_dev_alert_to_alert_rule_org_id_organization_organizationid` FOREIGN KEY (`organization_id`) REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION
);

-- changeset voviedo:0048968a-fbc7-41cb-a003-bc09a1b39e80 context:main
INSERT INTO device_alert_to_alert_rule (
    device_alert_id,
    device_alert_rule_id,
    iteration,
    started_on,
    organization_id
)
SELECT
    device_alert_id,
    device_alert_rule_id,
    iteration,
    started_on,
    organization_id
FROM device_alert WHERE device_alert_rule_id IS NOT NULL;

-- changeset voviedo:923fcfb9-66da-4c07-a38f-3f83074a4dd8 context:main
ALTER TABLE IF EXISTS `device_alert`
	DROP COLUMN IF EXISTS `device_alert_rule_id`,
	DROP COLUMN IF EXISTS `started_on`,
	DROP COLUMN IF EXISTS `iteration`,
	DROP FOREIGN KEY IF EXISTS `FK_device_alert_device_alert_rule`;

-- changeset voviedo:9ceee5c0-ccc1-4c65-92fa-a741b8864dc1 context:main
ALTER TABLE IF EXISTS device_alert_rule ADD COLUMN IF NOT EXISTS keep_searching_in_children_organizations BIT NOT NULL DEFAULT 0;

-- changeset voviedo:2fe21984-7f45-4dbd-9327-9a3519354db5 context:main
ALTER TABLE IF EXISTS device_alert_escalation_chain_notified ADD COLUMN IF NOT EXISTS organization_id INT NULL; 
ALTER TABLE IF EXISTS device_alert_escalation_chain_notified ADD COLUMN IF NOT EXISTS device_alert_to_alert_rule_id INT NULL; 

-- changeset voviedo:22a55b05-d40e-4bc2-b7c6-d3bcc105a461 context:main
UPDATE device_alert_escalation_chain_notified SET 
organization_id = 
(select organization_id FROM device_alert WHERE 
	device_alert_escalation_chain_notified.device_alert_id = device_alert.device_alert_id), 
	device_alert_to_alert_rule_id = 
(select device_alert_to_alert_rule_id FROM device_alert_to_alert_rule WHERE 
	device_alert_escalation_chain_notified.device_alert_id = device_alert_to_alert_rule.device_alert_id);

-- changeset voviedo:23b444b4-a342-4af2-b66c-b6668975f990 context:main
ALTER TABLE IF EXISTS device_alert_escalation_chain_notified MODIFY  COLUMN IF EXISTS organization_id INT NOT NULL; 
ALTER TABLE IF EXISTS device_alert_escalation_chain_notified MODIFY  COLUMN IF EXISTS device_alert_to_alert_rule_id INT NOT NULL; 

-- changeset voviedo:b65a07a6-e3f1-4f42-b1af-de8eb8f4ab7d context:main
ALTER TABLE IF EXISTS `device_alert_escalation_chain_notified`
	ADD CONSTRAINT `fk_device_alert_esc_chain_notified_device_alert_to_alert_rule` 
	FOREIGN KEY IF NOT EXISTS (`device_alert_to_alert_rule_id`)  
	REFERENCES `device_alert_to_alert_rule` (`device_alert_to_alert_rule_id`) ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE IF EXISTS `device_alert` 
	ADD CONSTRAINT `fk_device_alert_organizationid_organization_organizationid` 
	FOREIGN KEY IF NOT EXISTS (`organization_id`)
	REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION;

ALTER TABLE IF EXISTS `device_alert_escalation_chain_notified` 
	ADD CONSTRAINT `fk_device_alert_esc_chain_notified_orgid_org_orgid` 
	FOREIGN KEY IF NOT EXISTS (`organization_id`)
	REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION;