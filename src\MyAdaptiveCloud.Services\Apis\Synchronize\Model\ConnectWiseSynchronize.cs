﻿using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Services.Apis.Synchronize.Model
{
    [ApplicationType(Applications.ConnectWise)]
    public class ConnectWiseSynchronize : ISynchronize
    {
        private readonly MyAdaptiveCloudContext _dbContext;

        public ConnectWiseSynchronize(MyAdaptiveCloudContext context)
        {
            _dbContext = context;
        }

        public async Task UpdateOrganizationName(int organizationId, string name)
        {
            await Task.CompletedTask;
        }

        public async Task DisconnectApplication(int organizationId)
        {
            // TODO Should we remove the CW Company?
            await Task.CompletedTask;
        }
    }
}
