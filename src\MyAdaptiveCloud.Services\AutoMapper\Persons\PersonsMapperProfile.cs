﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Person;
using MyAdaptiveCloud.Services.DTOs.Role;

namespace MyAdaptiveCloud.Services.AutoMapper.Persons
{
    public class PersonsMapperProfile : Profile
    {
        public PersonsMapperProfile()
        {
            CreateMap<UnAssignedUserByRole, PersonDTO>();

            CreateMap<Person, PersonDTO>()
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.PersonId));

            CreateMap<UnAssignedUserByRole, UnAssignedUserByRoleDTO>();

            CreateMap<Person, ReportRecipientPersonDTO>()
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.PersonId));
        }
    }
}