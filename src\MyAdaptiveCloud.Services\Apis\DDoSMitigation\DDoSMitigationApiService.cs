﻿using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Services.Apis.DDoSMitigation.Constants;
using MyAdaptiveCloud.Services.Apis.DDoSMitigation.Models;
using MyAdaptiveCloud.Services.Apis.DDoSMitigation.Responses;
using MyAdaptiveCloud.Services.Apis.Tenax;
using MyAdaptiveCloud.Services.DTOs.DDoSMitigation;
using MyAdaptiveCloud.Services.Requests.DDoSMitigation;
using MyAdaptiveCloud.Services.Services;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.DDoSMitigation
{
    public class DDoSMitigationApiService : IDDoSMitigationApiService
    {
        private readonly HttpClient _client;
        private readonly IConfigurationService _configurationService;
        private readonly ILogger _logger;
        private DDoSMitigationConfigurationDTO _ddosConfiguration;

        private readonly JsonSerializerOptions _jsonSerializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
        };

        public DDoSMitigationApiService(
            HttpClient client,
            ILogger<TenaxApi> logger,
            IConfigurationService configurationService
        )
        {
            _client = client;
            _logger = logger;
            _configurationService = configurationService;

            _ddosConfiguration = _configurationService.GetDDoSMitigationConfiguration().GetAwaiter().GetResult();
            _client.BaseAddress = new Uri(_ddosConfiguration.BaseURL);
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public async Task<List<Bgp>> GetBGP()
        {
            var listResponse = await GetAsync<DDoSMitigationApiListResponse<Bgp>>(DDoSMitigationApiEndpoints.GetBGP);
            var bgpList = listResponse?.Data ?? [];
            return bgpList;
        }

        public async Task<List<Bgp>> GetAdvertisedBGP()
        {
            var dictResponse = await GetAsync<DDoSMitigationAdvertisedBGPApiResponse>(DDoSMitigationApiEndpoints.GetAdvertisedBGP);
            return FlattenAdvertisedBgpResponse(dictResponse);
        }

        private List<Bgp> FlattenAdvertisedBgpResponse(DDoSMitigationAdvertisedBGPApiResponse response)
        {
            var result = new List<Bgp>();

            foreach (var outer in response.Data)
            {
                foreach (var inner in outer.Value)
                {
                    var raw = inner.Value;
                    var bgp = new Bgp
                    {
                        Id = inner.Key, // store the prefix as the ID (e.g., "*************/32")
                        NeighborIp = raw.NeighborIp,
                        NeighborRemoteAs = raw.NeighborRemoteAs,
                        Prefix = raw.Prefix,
                        NextHop = raw.NextHop,
                        AsPath = raw.AsPath ?? new List<int>(),
                        Communities = raw.Communities,
                        Med = raw.Med,
                        LocalPref = raw.LocalPref,
                        Origin = raw.Origin,
                        Aggregator = raw.Aggregator,
                        AtomicAggregate = raw.AtomicAggregate,
                        ExtendedCommunities = raw.ExtendedCommunities,
                        LargeCommunities = raw.LargeCommunities,
                        ClusterList = raw.ClusterList ?? new List<string>(),
                        OriginatorId = raw.OriginatorId,
                        Simulate = raw.Simulate
                    };

                    result.Add(bgp);
                }
            }

            return result;
        }


        public async Task<List<Blackhole>> GetBlackholeStatus()
        {
            var dictResponse = await GetAsync<DDoSMitigationApiDictionaryResponse<Blackhole>>(DDoSMitigationApiEndpoints.GetBlackholeStatus);
            var dict = dictResponse?.Data ?? new Dictionary<string, Blackhole>();
            return dict.Select(kvp =>
            {
                kvp.Value.CIDR = kvp.Key;
                return kvp.Value;
            }).ToList();
        }

        public async Task<List<Neighbor>> GetNeighbors()
        {
            var listResponse = await GetAsync<DDoSMitigationApiListResponse<Neighbor>>(DDoSMitigationApiEndpoints.GetNeighbors);
            var list = listResponse?.Data ?? new List<Neighbor>();
            return list;
        }

        public async Task<Health> GetHealth()
        {
            var response = await GetAsync<Health>(DDoSMitigationApiEndpoints.GetHealth);
            return response;
        }

        public async Task<List<TaskDetail>> GetTasks()
        {
            var dictResponse = await GetAsync<DDoSMitigationApiDictionaryResponse<TaskDetail>>(DDoSMitigationApiEndpoints.GetTasks);
            var dict = dictResponse?.Data ?? new Dictionary<string, TaskDetail>();
            return dict.Select(kvp =>
            {
                kvp.Value.Id = kvp.Key;
                return kvp.Value;
            }).ToList();
        }

        public async Task<TaskDetail> GetTask(string taskId)
        {
            var dictResponse = await GetAsync<DDoSMitigationApiDictionaryResponse<TaskDetail>>(string.Format(DDoSMitigationApiEndpoints.GetTask, taskId));
            var dict = dictResponse?.Data ?? new Dictionary<string, TaskDetail>();
            return dict.Select(kvp =>
            {
                kvp.Value.Id = kvp.Key;
                return kvp.Value;
            }).FirstOrDefault();
        }

        public async Task<List<Scrub>> GetScrubStatus()
        {
            var dictResponse = await GetAsync<DDoSMitigationApiDictionaryResponse<Scrub>>(DDoSMitigationApiEndpoints.GetScrubStatus);
            var dict = dictResponse?.Data ?? new Dictionary<string, Scrub>();
            return dict.Select(kvp =>
            {
                kvp.Value.CIDR = kvp.Key;
                return kvp.Value;
            }).ToList();
        }

        public async Task<DDoSMitigationTaskResponse> StartBlackhole(DdosStartRequest request)
        {
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "prefix", request.Prefix },
                { "simulate", request.Simulate }
            };

            return await PostAsync(DDoSMitigationApiEndpoints.StartBlackhole, requestParams);
        }

        public async Task<DDoSMitigationTaskResponse> StopBlackhole(DdosStopRequest request)
        {
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "prefix", request.Prefix },
            };

            return await PostAsync(DDoSMitigationApiEndpoints.StopBlackhole, requestParams);
        }

        public async Task<DDoSMitigationTaskResponse> StartScrub(DdosStartRequest request)
        {
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "prefix", request.Prefix },
                { "simulate", request.Simulate }
            };

            return await PostAsync(DDoSMitigationApiEndpoints.StartScrub, requestParams);
        }

        public async Task<DDoSMitigationTaskResponse> StopScrub(DdosStopRequest request)
        {
            Dictionary<string, object> requestParams = new Dictionary<string, object>
            {
                { "prefix", request.Prefix },
            };

            return await PostAsync(DDoSMitigationApiEndpoints.StopScrub, requestParams);
        }

        public async Task<MitigationValidation> ValidateScrub(string cidr)
        {
            var requestParams = new Dictionary<string, string>
            {
                {
                    "prefix",
                    cidr
                }
            };
            try
            {
                return await GetAsync<MitigationValidation>(DDoSMitigationApiEndpoints.ValidateScrub, requestParams);
            }
            catch (HttpRequestException ex)
            {
                if (ex.StatusCode == HttpStatusCode.UnprocessableContent)
                {
                    return new MitigationValidation
                    {
                        Messages = [ex.Message],
                    };
                }

                throw;
            }
        }

        public async Task<MitigationValidation> ValidateBlackhole(string cidr)
        {
            var requestParams = new Dictionary<string, string>
            {
                {
                    "prefix",
                    cidr
                }
            };
            try
            {
                return await GetAsync<MitigationValidation>(DDoSMitigationApiEndpoints.ValidateBlackhole, requestParams);
            }
            catch (HttpRequestException ex)
            {
                if (ex.StatusCode == HttpStatusCode.UnprocessableContent)
                {
                    return new MitigationValidation
                    {
                        Messages = [ex.Message],
                    };
                }

                throw;
            }
        }

        private async Task<TResponse> GetAsync<TResponse>(string endpoint, Dictionary<string, string> requestParams = null)
        {
            var baseUrl = $"{_ddosConfiguration.BaseURL}/{endpoint}";
            var requestUrl = requestParams != null && requestParams.Count > 0
                ? QueryHelpers.AddQueryString(baseUrl, requestParams)
                : QueryHelpers.AddQueryString(baseUrl, new Dictionary<string, string>());
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            var response = await SendAsync(httpRequestMessage);

            var responseStream = await response.Content.ReadAsStreamAsync();

            if (responseStream == null || responseStream.Length == 0)
            {
                return default; // or throw an exception if you expect a response
            }

            var parsedResponse = await JsonSerializer.DeserializeAsync<TResponse>(responseStream, _jsonSerializerOptions);
            return parsedResponse;
        }


        private async Task<DDoSMitigationTaskResponse> PostAsync(string endpoint, Dictionary<string, object> requestParams)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, $"{_ddosConfiguration.BaseURL}/{endpoint}");

            var jsonRequest = JsonSerializer.Serialize(requestParams, (JsonSerializerOptions)null);
            httpRequestMessage.Content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            var response = await SendAsync(httpRequestMessage);

            var responseStream = await response.Content.ReadAsStreamAsync();
            var parsedResponse = await JsonSerializer.DeserializeAsync<DDosMitigationApiResponse<DDoSMitigationTaskResponse>>(responseStream, _jsonSerializerOptions);

            return parsedResponse?.Data;
        }

        private async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request)
        {
            HttpResponseMessage response = null;
            try
            {
                var username = _ddosConfiguration.Username;
                var password = _ddosConfiguration.Password;
                var byteArray = Encoding.ASCII.GetBytes($"{username}:{password}");
                var headerValue = Convert.ToBase64String(byteArray);
                request.Headers.Authorization = new AuthenticationHeaderValue("Basic", headerValue);
                response = await _client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return response;
            }
            catch (HttpRequestException ex)
            {
                // UnprocessableContent errors are excluded from logging because they are expected in certain scenarios such as mitigation validation
                if (ex.StatusCode != HttpStatusCode.UnprocessableContent)
                {
                    _logger.LogError(ex, "DDoS Mitigation API error");
                }

                if (response != null && response.Content != null)
                {
                    var errorContent = await response.Content.ReadAsStreamAsync();
                    var parsedErrorContent = await JsonSerializer.DeserializeAsync<DDosMitigationApiErrorResponse>(errorContent, _jsonSerializerOptions);
                    throw new HttpRequestException(parsedErrorContent.Detail, ex, ex.StatusCode);
                }

                throw;
            }
        }
    }
}