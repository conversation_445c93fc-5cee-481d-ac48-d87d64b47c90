namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Svm : OntapObject
{
    public Ipspace Ipspace { get; set; }
    public SvmState? State { get; set; }
    public List<IpInterface> IpInterfaces { get; set; }
    public List<Aggregate> Aggregates { get; set; }
    public List<Route> Routes { get; set; }
    public SvmDns Dns { get; set; }
    public SvmCifs Cifs { get; set; }
    public SvmNfs Nfs { get; set; }

    public Svm()
    {
    }

    public Svm(Guid uuid) : base(uuid)
    {
    }

    public Svm(string name, Guid ipspaceUuid, Aggregate aggregate) : base(name)
    {
        Ipspace = new Ipspace(ipspaceUuid);
        Aggregates = new List<Aggregate> { new Aggregate { Name = aggregate.Name, Uuid = aggregate.Uuid } };
    }

    public override string ToString()
    {
        var interfaces = IpInterfaces?.ConvertAll(i => $"IP Name: {i.Name} IP: {i.Ip.Address}") ?? new List<string>();
        var ipInterfaces = string.Join(", ", interfaces);
        var ipspaceName = Ipspace?.Name;
        return $"SVM Name: {Name} IP Space: {ipspaceName} State: {State} IP Interfaces: {ipInterfaces}";
    }
}

public enum SvmState
{
    Starting,
    Running,
    Stopping,
    Stopped,
    Deleting
}

public class SvmDns
{
    public List<string> Servers { get; set; }
    public List<string> Domains { get; set; }
}

public class SvmCifs : OntapObject
{
    public bool Enabled { get; set; }
    public SvmCifsAdDomain AdDomain { get; set; }
    public Svm Svm { get; set; }

    public SvmCifs() { }

    public SvmCifs(string name, Guid svm)
    {
        Enabled = true;
        Name = name;
        Svm = new Svm(svm);
    }

    public override string ToString()
    {
        return $"CIFS {Name} {Enabled}";
    }
}

public class SvmCifsAdDomain
{
    public string Fqdn { get; set; }
    public string OrganizationalUnit { get; set; }
    public string User { get; set; }
    public string Password { get; set; }
}

public class SvmNfs
{
    public bool Enabled { get; set; }
    public Svm Svm { get; set; }

    public SvmNfs()
    {
    }

    public SvmNfs(Guid svm)
    {
        Svm = new Svm(svm);
        Enabled = true;
    }

    public override string ToString()
    {
        return $"NFS: {Enabled} {Svm}";
    }
}

public class UpdateSvm
{
    public UpdateSvm() { }
    public string Name { get; set; }
    public UpdateSvm(string name)
    {
        Name = name;
    }
}