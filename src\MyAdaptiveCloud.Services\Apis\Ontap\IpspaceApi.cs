using MyAdaptiveCloud.Services.Apis.Ontap.Model;

namespace MyAdaptiveCloud.Services.Apis.Ontap;

public class IpspaceApi : TypeApi
{
    public IpspaceApi(OntapApi client) : base(client)
    {
    }

    public async Task<List<Ipspace>> GetIpSpaces()
    {
        return await Client.GetResources<Ipspace>(UrlBuilder.ListUrl("/network/ipspaces"));
    }

    public async Task<Ipspace> GetIpSpace(string name)
    {
        return await Client.SearchResource<Ipspace>(new UrlBuilder("/network/ipspaces").AddFields().AddQueryParam("name", name));
    }

    public async Task<Ipspace> GetIpSpace(Guid uuid)
    {
        return await Client.SearchResource<Ipspace>(new UrlBuilder("/network/ipspaces/%uuid%").AddFields().AddPathParam("uuid", uuid));
    }

    public async Task<Ipspace> CreateIpSpace(string name)
    {
        var body = new Ipspace(name.ToLowerInvariant());
        return await Client.CreateResource(new UrlBuilder("/network/ipspaces"), body);
    }

    public async Task UpdateIpSpace(string name, Guid uuid)
    {
        var body = new UpdateIpspace(name.ToLowerInvariant());
        await Client.PatchResource(UrlBuilder.ResourceUrl("/network/ipspaces", uuid), body);
    }

    public async Task DeleteIpSpace(Guid uuid)
    {
        await Client.DeleteResource(UrlBuilder.ResourceUrl("/network/ipspaces", uuid));
    }

    public async Task<Job> DeleteIpSpace(Ipspace ipspace)
    {
        return await Client.DeleteResourceAsync(UrlBuilder.ResourceUrl("/network/ipspaces", ipspace.Uuid));
    }
}