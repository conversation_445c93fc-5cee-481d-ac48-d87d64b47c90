using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Msrc.Model
{
    public class CvrfUpdateResponse
    {
        [JsonPropertyName("value")]
        public List<CvrfUpdate> Value { get; set; } = [];
    }

    public class CvrfUpdate
    {
        [JsonPropertyName("ID")]
        public string Id { get; set; } = null!;

        [JsonPropertyName("DocumentTitle")]
        public string DocumentTitle { get; set; }

        [JsonPropertyName("InitialReleaseDate")]
        public DateTime? InitialReleaseDate { get; set; }

        [JsonPropertyName("CurrentReleaseDate")]
        public DateTime? CurrentReleaseDate { get; set; }

        [JsonPropertyName("CvrfUrl")]
        public string CvrfUrl { get; set; }
    }
}