﻿namespace MyAdaptiveCloud.Services.Apis.Ontap.Model.CliRequests
{
    public class VserverCifsUpdateActiveDirectory
    {
        public string Domain { get; set; }
        public string AdminPassword { get; set; }
        public string AdminUsername { get; set; }
        public string StatusAdmin { get; set; } = "down";

        public VserverCifsUpdateActiveDirectory()
        {
        }

        public VserverCifsUpdateActiveDirectory(string domain, string user, string password)
        {
            Domain = domain;
            AdminPassword = password;
            AdminUsername = user;
        }
    }
}