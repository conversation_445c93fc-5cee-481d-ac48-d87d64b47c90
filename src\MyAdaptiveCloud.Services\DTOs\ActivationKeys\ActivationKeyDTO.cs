﻿using MyAdaptiveCloud.Core.Common.UserActions;
using MyAdaptiveCloud.Core.Permissions;

namespace MyAdaptiveCloud.Services.DTOs.ActivationKeys
{
    public class ActivationKeyDTO : IUserAction
    {
        public int ActivationKeyId { get; set; }

        public string Key { get; set; }

        public DateTimeOffset? CreationDate { get; set; }

        public DateTimeOffset? DeactivationDate { get; set; }

        public UserActionStateEnum CanView { get; set; }

        public UserActionStateEnum CanCreate { get; set; }

        [UserAction(Perms.ManageActivationKey)]
        public UserActionStateEnum CanEdit { get; set; }

        public UserActionStateEnum CanDelete { get; set; }

        public int OrganizationId { get; set; }

        public bool IsActive { get; set; }
    }
}