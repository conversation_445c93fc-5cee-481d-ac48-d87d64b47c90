-- liquibase formatted sql

-- changeset lcerbino:5e6122bb-7021-4b36-8d41-8af18afb14c6 context:main
SET @RoleId = (Select RoleId from Role where Name = 'Admin');
CALL `SetPermission`(@RoleId, 52, true);

-- changeset lcerbino:ae1932f2-5562-40dc-b4d6-9385973371ff context:main
SET @RoleId = (Select RoleId from Role where Name = 'Super Admin');
CALL `SetPermission`(@RoleId, 52, true);

-- changeset lcerbino:b3f95aea-5055-4a0f-9ec9-0587be0e5d8e context:main
SET @RoleId = (Select RoleId from Role where Name = 'Admin');
CALL `SetPermission`(@RoleId, 53, true);

-- changeset lcerbino:44f64b53-301e-40ed-9da6-65bd1f479822 context:main
SET @RoleId = (Select RoleId from Role where Name = 'Super Admin');
CALL `SetPermission`(@RoleId, 53, true);