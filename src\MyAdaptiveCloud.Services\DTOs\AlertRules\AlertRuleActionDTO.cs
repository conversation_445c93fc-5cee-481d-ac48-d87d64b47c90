﻿namespace MyAdaptiveCloud.Services.DTOs.AlertRules
{
    public class AlertRuleActionDTO
    {
        public int DeviceAlertRuleActionId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public AlertRuleActionEnumDTO Value { get; set; }
    }

    public enum AlertRuleActionEnumDTO
    {
        DoNothing = 1,
        AutoAcknowledge,
        StartEscalationChain
    }
}
