-- liquibase formatted sql

-- changeset lcerbino:6dfd22f1-fed4-48a5-bf9e-e5ff6859c4e5 context:main
CREATE OR REPLACE TABLE `file_administration` (
    `file_administration_id` INT NOT NULL AUTO_INCREMENT,
    `organization_id` INT NOT NULL,
    `name` VARCHAR(1000) NOT NULL,
    `created_by` INT,
    `updated_by` INT,
    `created_on` DATETIME NOT NULL,
    `updated_on` DATETIME,
    `file_size` DECIMAL(10,2),
    PRIMARY KEY (`file_administration_id`),
    CONSTRAINT `fk_file_administration_user_id` FOREIGN KEY (`created_by`) REFERENCES `User` (`UserId`) ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT `fk_file_administration_org_OrganizationId` FOREIGN KEY (`organization_id`) REFERENCES `Organization` (`OrganizationId`) ON UPDATE NO ACTION ON DELETE NO ACTION   
);

-- changeset lcerbino:e2e1c62b-5931-4ec8-b5ad-c72fcd643e33 context:main
CALL AddConfigurationCategory('File Administration');
CALL SetConfigurationValue('File Administration','FolderPath','','input', 0);
CALL SetConfigurationValue('File Administration','MaximumFileSize','2048','input', 0);

-- changeset lcerbino:1DB7992C-5CB7-4E3E-B519-478EAF8E3E4D context:test
CALL SetConfigurationValue('File Administration','FolderPath','file','input', 0);