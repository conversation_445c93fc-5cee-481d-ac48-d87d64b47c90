using MyAdaptiveCloud.Services.DTOs.Configuration;

namespace MyAdaptiveCloud.Services.Apis.Ontap.Model;

public class Volume : OntapObject
{
    public Svm Svm { get; set; }
    public List<Aggregate> Aggregates { get; set; }
    public long Size { get; set; }

    public VolumeNas Nas { get; set; }

    public VolumeAutosize Autosize { get; set; }
    public VolumeGuarantee Guarantee { get; set; }
    public VolumeSnapshotPolicy SnapshotPolicy { get; set; }

    public VolumeSpace Space { get; set; }

    public Volume()
    {
    }

    public Volume(Guid svm, string aggregate, string name, string mountPath, long size, bool autoSize, string snapshotPolicy, NetworkFileServersConfigurationDTO configuration)
    {
        Name = name;
        Svm = new Svm(svm);
        Aggregates = new List<Aggregate> { new(aggregate) };
        Nas = new VolumeNas(mountPath);
        if (autoSize)
        {
            Size = size;
            Autosize = new VolumeAutosize(configuration.FileSizeGrowthThreshold, configuration.FileSizeShrinkThreshold, VolumeAutosizeMode.grow_shrink,
                ConvertHelpers.ConvertGigabytesToBytes(configuration.MaximumAutoSize), size);
        }
        else
        {
            Size = size;
            Autosize = null;
        }

        Guarantee = new VolumeGuarantee(VolumeSpaceGuaranteeType.none);
        SnapshotPolicy = new VolumeSnapshotPolicy(snapshotPolicy);
    }

    public override string ToString()
    {
        var aggregates = String.Join(", ", Aggregates?.Select(a => a.Name).ToList() ?? new List<string>());
        return $"Volume: {Name} Size: {Size} Svm: {Svm.Name} Aggregate: {aggregates}";
    }

    public bool IsAutosize()
    {
        return Autosize?.Mode == VolumeAutosizeMode.grow_shrink;
    }
}

public class VolumeUpdate
{
    public long Size { get; set; }
    public VolumeAutosize Autosize { get; set; }
    public VolumeSnapshotPolicy SnapshotPolicy { get; set; }

    public VolumeUpdate()
    {
    }

    public VolumeUpdate(long size, bool autoSize, string snapshotPolicy, bool updateSize, NetworkFileServersConfigurationDTO configuration)
    {
        if (autoSize && updateSize)
        {
            Size = size;
            Autosize = new VolumeAutosize(configuration.FileSizeGrowthThreshold, configuration.FileSizeShrinkThreshold, VolumeAutosizeMode.grow_shrink,
                ConvertHelpers.ConvertGigabytesToBytes(configuration.MaximumAutoSize), size);
        }
        else
        {
            Size = size;
            Autosize = null;
        }

        SnapshotPolicy = new VolumeSnapshotPolicy(snapshotPolicy);
    }
}

public class VolumeUpdateName
{
    public string Name { get; set; }

    public VolumeUpdateName()
    {
    }

    public VolumeUpdateName(string name)
    {
        Name = name;
    }
}

public class VolumeNas
{
    public string Path { get; set; }
    public string SecurityStyle { get; set; }

    public VolumeNas()
    {
    }

    public VolumeNas(string mountPath)
    {
        Path = mountPath;
        SecurityStyle = "mixed";
    }
}

public class VolumeAutosize
{
    public int GrowThreshold { get; set; }
    public int ShrinkThreshold { get; set; }
    public VolumeAutosizeMode Mode { get; set; }
    public long Maximum { get; set; }
    public long Minimum { get; set; }

    public VolumeAutosize()
    {
        Mode = VolumeAutosizeMode.off;
    }

    public VolumeAutosize(int growThreshold, int shrinkThreshold, VolumeAutosizeMode mode, long maximum, long size)
    {
        GrowThreshold = growThreshold;
        ShrinkThreshold = shrinkThreshold;
        Mode = mode;
        Maximum = maximum;
        Minimum = size - 1;
    }
}

public class VolumeGuarantee
{
    public VolumeSpaceGuaranteeType Type { get; set; }

    public VolumeGuarantee()
    {
    }

    public VolumeGuarantee(VolumeSpaceGuaranteeType type)
    {
        Type = type;
    }
}

public class VolumeSnapshotPolicy
{
    public string Name { get; set; }

    public VolumeSnapshotPolicy()
    {
    }

    public VolumeSnapshotPolicy(string name)
    {
        Name = name;
    }
}

public class VolumeSpace
{
    public long Size { get; set; }
    public long Available { get; set; }
    public long Used { get; set; }
}

public enum VolumeAutosizeMode
{
    grow,
    grow_shrink,
    off
}

public enum VolumeSpaceGuaranteeType
{
    volume,
    none
}

public class ConvertHelpers
{
    public static long ConvertGigabytesToBytes(double gigabytes)
    {
        return (long)(gigabytes * 1024 * 1024 * 1024);
    }
}