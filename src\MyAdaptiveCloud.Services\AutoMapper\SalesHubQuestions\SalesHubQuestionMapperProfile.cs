﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.SalesHubQuestions;
using MyAdaptiveCloud.Services.DTOs.SalesHubQuestions;
using MyAdaptiveCloud.Services.Requests.SalesHubQuestions;

namespace MyAdaptiveCloud.Services.AutoMapper.SalesHubQuestions
{
    public class SalesHubQuestionMapperProfile : Profile
    {
        public SalesHubQuestionMapperProfile()
        {
            CreateMap<SalesHubQuestion, SalesHubQuestionDTO>();

            CreateMap<SalesHubQuestionType, SalesHubQuestionTypeDTO>();

            CreateMap<CreateSalesHubQuestionRequest, SalesHubQuestion>().ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.QuestionId, opt => opt.Ignore())
                .ForMember(dest => dest.QuestionType, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.Ignore());

            CreateMap<EditSalesHubQuestionRequest, SalesHubQuestion>()
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.QuestionType, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.Ignore());
        }
    }
}