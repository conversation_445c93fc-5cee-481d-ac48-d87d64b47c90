namespace MyAdaptiveCloud.Services.DTOs.AuthenticationKeys
{

    public class CreateAuthenticationKeyDTO
    {
        public int Id { get; set; }

        public int PersonId { get; set; }

        public string Name { get; set; }

        public string ApiKey { get; set; }

        /// <summary>
        /// This attribute should never be returned except when creating an AuthenticationKey
        /// </summary>
        public string ApiSecret { get; set; }

        public bool RequireSigning { get; set; }
    }
}