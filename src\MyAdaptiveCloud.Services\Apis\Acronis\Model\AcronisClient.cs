﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisClient
    {
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }
        #nullable enable
        [JsonPropertyName("client_secret")]
        public string? SecretKey { get; set; }
        [JsonPropertyName("registration_access_token")]
        public string? RegistrationAccessToken { get; set; }
        #nullable disable
        [Json<PERSON>ropertyName("client_secret_expires_at")]
        public int ClientSecretExpiratesAt { get; set; }
        [JsonPropertyName("type")]
        public string Type { get; set; }
        [JsonPropertyName("tenant_id")]
        public string TenantId { get; set; }
        [JsonPropertyName("data")]
        public AcronisClientData Data { get; set; }
        [JsonPropertyName("token_endpoint_auth_method")]
        public string TokenAuthMethod { get; set; }
        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }
        [JsonPropertyName("created_by")]
        public string CreatedBy { get; set; }
        [JsonPropertyName("last_access_at")]
        public string LastAccessAt { get; set; }
        [JsonPropertyName("last_access_from_ip")]
        public string LastAccessIp { get; set; }
        [JsonPropertyName("status")]
        public string Status { get; set; }
        [JsonPropertyName("redirect_uris")]
        public List<string> RedirectUris { get; set; }
    }
}
