﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisLocation
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("owner_tenant_id")]
        public Guid OwnerTenantId { get; set; }

        [JsonPropertyName("platform_owned")]
        public bool PlatformOwned { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("version")]
        public int Version { get; set; }

        [JsonPropertyName("readonly")]
        public bool Readonly { get; set; }
    }
}