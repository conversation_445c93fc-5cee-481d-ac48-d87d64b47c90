# MyAdaptiveCloud Dev SQL Scripts
This directory should only have scripts that are intended for use in the development and/or lab environment.
These scripts will never run in production.  They are intended to be used to create and/or seed the database for development and/or lab purposes.

One example where this is used is to turn on feature flags for features that are still in development but need to be tested in the lab/test environment.

## Contexts
Contexts to tag scripts with are:
* `dev` - Scripts that are intended for use in the development environment such as local development.
  * An example of this is turning on feature flags for features that are still in development and need to be available in the local development environment.
* `test` - Scripts that are intended for use in the lab/test environment.

Most scripts may end up being applicable/tagged with both `dev` and `test` contexts.

## Files
Files in this directory are:
* `sql/*.sql` - SQL scripts that are intended for use in the development and/or lab environment.
* `changelog.yaml` - Liquibase changelog file.
* `apply_dev_updates.sh` - <PERSON><PERSON>t to apply the changes in `changelog-dev.yaml` file to the local database for development purposes for scripts tagged with `dev` context.
  * You can run this from macOS/Linux or from MSCode's/VSCode's integrated terminal.
* `apply_test_updates.sh` - <PERSON><PERSON><PERSON> to apply the changes in `changelog-dev.yaml` file to the lab/test database for testing purposes for scripts tagged with `test` context.
  * You can run this from macOS/Linux or from MSCode's/VSCode's integrated terminal.