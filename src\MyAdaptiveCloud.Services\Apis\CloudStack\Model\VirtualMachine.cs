using System.Text.Json.Serialization;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;

namespace MyAdaptiveCloud.Services.Apis.CloudStack.Model
{
    public class VirtualMachine
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Account { get; set; }
        public Guid DomainId { get; set; }
        [JsonPropertyName("domain")]
        public string DomainName { get; set; }
        public string State { get; set; }

        [JsonConverter(typeof(CloudStackDateTimeConverter))]
        public DateTime Created { get; set; }

        public List<Nic> Nic { get; set; }
    }

    public class ListVirtualMachinesResponse : CloudStackListResponse<VirtualMachine>
    {
        [JsonPropertyName("virtualMachine")]
        public override List<VirtualMachine> Items { get; set; }
    }
}