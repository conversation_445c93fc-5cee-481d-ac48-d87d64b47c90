﻿using System.Text.Json.Serialization;

namespace MyAdaptiveCloud.Services.Apis.Acronis.Model
{
    public class AcronisToken
    {
        [JsonPropertyName("access_token")]
        public string Token { get; set; }
        [JsonPropertyName("token_type")]
        public string Type { get; set; }
        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }
        [JsonPropertyName("expires_on")]
        public int ExpiresOn { get; set; }
        [JsonPropertyName("id_token")]
        public string TokenId { get; set; }
        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; } 
    }
}
